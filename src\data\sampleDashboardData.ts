
import { Project, Agent, PerformanceMetric } from "@/types/dashboard";

// Sample projects data with good contrast colors
export const sampleProjects: Project[] = [
  { id: "all", name: "All", code: "A", color: "bg-blue-500" },
  { id: "dtss", name: "DTS<PERSON>", code: "D", color: "bg-blue-500" },
  { id: "sis", name: "SIS NAG", code: "S", color: "bg-red-500" },
  { id: "opex", name: "Opex", code: "OPX", color: "bg-orange-500" },
  { id: "rare", name: "Rare", code: "RA", color: "bg-green-500" },
  { id: "sis2", name: "SIS 2.0", code: "S2", color: "bg-purple-500" },
  { id: "ufirm", name: "Ufirm", code: "UF", color: "bg-red-500" },
  { id: "dtss2", name: "DTSS", code: "DT", color: "bg-blue-500" },
  { id: "dale", name: "<PERSON>", code: "DC", color: "bg-orange-500" },
  { id: "priya", name: "Priya Living", code: "PL", color: "bg-purple-500" },
  { id: "uniq", name: "UniQ", code: "UQ", color: "bg-teal-500" },
  { id: "hungerbox", name: "HungerBox", code: "HB", color: "bg-teal-500" },
];

// Sample agents data
export const sampleAgents: Agent[] = [
  { id: "1", name: "Rajesh Choudhari", projectId: "dtss" },
  { id: "2", name: "Kalpana Verma", projectId: "sis" },
  { id: "3", name: "Manoj Bisht", projectId: "sis" },
  { id: "4", name: "Aditi Tiwari", projectId: "sis" },
  { id: "5", name: "Adarsh Singh", projectId: "sis" },
  { id: "6", name: "Vikas Thapa", projectId: "sis" },
  { id: "7", name: "Neha Rana", projectId: "sis" },
  { id: "8", name: "Ayush Bisht", projectId: "sis" },
  { id: "9", name: "Pooja", projectId: "dtss" },
  { id: "10", name: "Rushali", projectId: "dtss" },
  { id: "11", name: "Nandini", projectId: "dtss" },
  { id: "12", name: "Anushka", projectId: "dtss" },
  { id: "13", name: "Jaskirat", projectId: "dtss" },
  { id: "14", name: "Susritha", projectId: "dtss" },
];

// Helper function to get week number of month
const getWeekOfMonth = (date: Date): string => {
  const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
  const dayOfMonth = date.getDate();
  const dayOfWeek = firstDay.getDay();
  const weekNumber = Math.ceil((dayOfMonth + dayOfWeek) / 7);
  return `Week ${Math.min(weekNumber, 5)}`;
};

// Function to generate sample metrics
export const generateSampleMetrics = (): PerformanceMetric[] => {
  const metrics: PerformanceMetric[] = [];
  const today = new Date();

  // Generate data for the last 90 days to cover multiple months
  for (let i = 0; i < 90; i++) {
    const date = new Date();
    date.setDate(today.getDate() - i);

    // For each agent, create some random metrics
    sampleAgents.forEach(agent => {
      const dials = Math.floor(Math.random() * 50) + 30;
      const connected = Math.floor(dials * (Math.random() * 0.6 + 0.2));
      const talkTime = connected * (Math.random() * 20 + 5);
      const scheduledMeetings = Math.floor(connected * (Math.random() * 0.3 + 0.1));
      const successfulMeetings = Math.floor(scheduledMeetings * (Math.random() * 0.8 + 0.2));
      const successRate = (successfulMeetings / scheduledMeetings) * 100 || 0;

      metrics.push({
        id: `${agent.id}-${date.toISOString().split('T')[0]}`,
        agentId: agent.id,
        projectId: agent.projectId,
        date: date.toISOString().split('T')[0],
        week: getWeekOfMonth(date),
        dials,
        connected,
        talkTime,
        scheduledMeetings,
        successfulMeetings,
        successRate: Math.round(successRate * 10) / 10,
      });
    });
  }

  return metrics;
};
