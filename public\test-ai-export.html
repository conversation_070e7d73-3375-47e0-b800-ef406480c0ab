<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Export Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-input { font-weight: bold; color: #0066cc; }
        .test-result { margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; border-left: 4px solid #dc3545; }
        button { padding: 10px 20px; margin: 10px 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>AI Smart Export Test</h1>
    <p>This page tests the AI Smart Export functionality with various input styles.</p>
    
    <button onclick="runAllTests()">Run All Tests</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script>
        const GEMINI_API_KEY = "AIzaSyArr9TLQHEBgTbrs4UDe0iGDdCaCicbXfQ";
        const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";

        const availableAgents = [
            "Rajesh Choudhari", "Kalpana Verma", "Manoj Bisht", "Aditi Tiwari", 
            "Adarsh Singh", "Vikas Thapa", "Neha Rana", "Ayush Bisht", 
            "Pooja", "Rushali", "Nandini", "Anushka", "Jaskirat", "Susritha"
        ];

        const availableProjects = [
            "DTSS", "SIS NAG", "Opex", "Rare", "SIS 2.0", "Ufirm", 
            "Dale Carnegie", "Priya Living", "UniQ", "HungerBox"
        ];

        const testCases = [
            {
                input: "Get DTSS project data from 1st to 15th June",
                expected: { projectName: "DTSS", timeFrame: "Daily", scope: "project" }
            },
            {
                input: "rajesh data from dtss 5 to 6 june",
                expected: { agentName: "Rajesh Choudhari", projectName: "DTSS", timeFrame: "Daily", scope: "agent" }
            },
            {
                input: "hungerbox week 1",
                expected: { projectName: "HungerBox", timeFrame: "Weekly", scope: "project" }
            },
            {
                input: "kalpana june data",
                expected: { agentName: "Kalpana Verma", timeFrame: "Monthly", scope: "agent" }
            },
            {
                input: "dc project performance",
                expected: { projectName: "Dale Carnegie", scope: "project" }
            },
            {
                input: "show me aditi performance 5 jun to 10 jun",
                expected: { agentName: "Aditi Tiwari", timeFrame: "Daily", scope: "agent" }
            },
            {
                input: "export all projects this month",
                expected: { timeFrame: "Monthly", scope: "all" }
            },
            {
                input: "sis data week 2",
                expected: { projectName: "SIS NAG", timeFrame: "Weekly", scope: "project" }
            }
        ];

        async function testAIExport(userRequest) {
            try {
                const prompt = `
You are an expert AI assistant that understands natural language requests for data export. You must be very forgiving and flexible with user input.

AVAILABLE DATA:
Agents: ${availableAgents.join(", ")}
Projects: ${availableProjects.join(", ")}

USER REQUEST: "${userRequest}"

INSTRUCTIONS:
1. Accept ANY style of English - formal, informal, broken grammar, typos, abbreviations
2. Be extremely flexible with date formats: "5 june", "5th June", "5 jun", "june 5", "05/06", "5-6-2025", etc.
3. Use fuzzy matching for names:
   - "dc" or "dale" → "Dale Carnegie"
   - "dtss" → "DTSS" 
   - "hungerbox" or "hunger" → "HungerBox"
   - "sis" → "SIS NAG"
   - "rajesh" → "Rajesh Choudhari"
   - "kalpana" → "Kalpana Verma"
   - "aditi" → "Aditi Tiwari"
   - Match partial names, first names, or abbreviations
4. Handle informal language: "get data", "download", "export", "show me", etc.
5. For current year 2025, assume dates without year are 2025
6. If user says "from X to Y" create a date range
7. If user mentions "week 1", "week 2" etc, use Weekly timeFrame
8. If user mentions month name only, use Monthly timeFrame

RETURN ONLY THIS JSON FORMAT (no markdown, no explanation):
{
  "agentName": "exact_match_from_list_or_null",
  "projectName": "exact_match_from_list_or_null",
  "timeFrame": "Daily",
  "startDate": "2025-MM-DD",
  "endDate": "2025-MM-DD", 
  "selectedWeek": null,
  "selectedMonth": null,
  "scope": "agent_or_project_or_all"
}

PARSE THIS REQUEST NOW:
`;

                const request = {
                    contents: [
                        {
                            parts: [{ text: prompt }]
                        }
                    ]
                };

                const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(request)
                });

                if (!response.ok) {
                    throw new Error(`API request failed: ${response.status}`);
                }

                const data = await response.json();
                
                if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                    throw new Error("Invalid response structure from AI");
                }
                
                const aiResponse = data.candidates[0].content.parts[0].text;
                
                // Clean up the response and parse JSON
                let cleanResponse = aiResponse.replace(/```json\n?|```\n?|```/g, '').trim();
                
                // Try to extract JSON if there's extra text
                const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    cleanResponse = jsonMatch[0];
                }
                
                const parsedRequest = JSON.parse(cleanResponse);
                return parsedRequest;
                
            } catch (error) {
                throw error;
            }
        }

        async function runTest(testCase, index) {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-case';
            resultDiv.innerHTML = `
                <div class="test-input">Test ${index + 1}: "${testCase.input}"</div>
                <div class="test-result">Testing...</div>
            `;
            document.getElementById('results').appendChild(resultDiv);

            try {
                const result = await testAIExport(testCase.input);
                
                // Check if result matches expected values
                let success = true;
                let details = [];
                
                if (testCase.expected.agentName && result.agentName !== testCase.expected.agentName) {
                    success = false;
                    details.push(`Expected agent: ${testCase.expected.agentName}, got: ${result.agentName}`);
                }
                
                if (testCase.expected.projectName && result.projectName !== testCase.expected.projectName) {
                    success = false;
                    details.push(`Expected project: ${testCase.expected.projectName}, got: ${result.projectName}`);
                }
                
                if (testCase.expected.timeFrame && result.timeFrame !== testCase.expected.timeFrame) {
                    success = false;
                    details.push(`Expected timeFrame: ${testCase.expected.timeFrame}, got: ${result.timeFrame}`);
                }
                
                if (testCase.expected.scope && result.scope !== testCase.expected.scope) {
                    success = false;
                    details.push(`Expected scope: ${testCase.expected.scope}, got: ${result.scope}`);
                }
                
                const resultElement = resultDiv.querySelector('.test-result');
                resultElement.className = `test-result ${success ? 'success' : 'error'}`;
                resultElement.innerHTML = `
                    <strong>${success ? '✅ PASSED' : '❌ FAILED'}</strong><br>
                    <strong>Result:</strong> ${JSON.stringify(result, null, 2)}<br>
                    ${details.length > 0 ? `<strong>Issues:</strong><br>${details.join('<br>')}` : ''}
                `;
                
            } catch (error) {
                const resultElement = resultDiv.querySelector('.test-result');
                resultElement.className = 'test-result error';
                resultElement.innerHTML = `<strong>❌ ERROR:</strong> ${error.message}`;
            }
        }

        async function runAllTests() {
            clearResults();
            for (let i = 0; i < testCases.length; i++) {
                await runTest(testCases[i], i);
                // Add small delay between tests
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Make functions available globally
        window.testAIExport = testAIExport;
        window.runAllTests = runAllTests;
        window.clearResults = clearResults;
    </script>
</body>
</html>
