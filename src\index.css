
/* Import enhanced animations and micro-interactions */
@import './styles/enhanced-animations.css';
@import './styles/enhanced-dropdown-animations.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221 91% 59%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 91% 59%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Enhanced chart colors */
    --chart-1: 262 83% 58%; /* Purple for scheduled */
    --chart-2: 142 76% 36%; /* Green for successful */
    --chart-3: 221 83% 53%; /* Blue for dials */
    --chart-4: 142 69% 58%; /* Light green for connected */
    --chart-5: 262 52% 47%; /* Dark purple for other metrics */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-amplior-background text-foreground;
  }
}

@layer components {
  .stat-card {
    @apply bg-white rounded-lg p-4 shadow-sm border border-gray-100;
  }
  
  .sidebar-item {
    @apply flex items-center gap-3 px-3 py-2 rounded-md text-gray-800 hover:bg-gray-100 transition-colors;
  }
  
  .sidebar-item.active {
    @apply bg-blue-50 text-amplior-primary font-medium;
  }
  
  .avatar {
    @apply flex items-center justify-center rounded-md w-8 h-8 font-medium text-sm;
  }

  /* Ensure good contrast for avatar text based on background color */
  .avatar.bg-yellow-500,
  .avatar.bg-yellow-400,
  .avatar.bg-amber-400,
  .avatar.bg-lime-400,
  .avatar.bg-green-400,
  .avatar.bg-emerald-400,
  .avatar.bg-teal-400,
  .avatar.bg-cyan-400,
  .avatar.bg-sky-400,
  .avatar.bg-blue-400,
  .avatar.bg-indigo-400,
  .avatar.bg-violet-400,
  .avatar.bg-purple-400,
  .avatar.bg-fuchsia-400,
  .avatar.bg-pink-400,
  .avatar.bg-rose-400,
  .avatar.bg-orange-400,
  .avatar.bg-red-400,
  .avatar.bg-gray-300,
  .avatar.bg-gray-400,
  .avatar.bg-slate-300,
  .avatar.bg-slate-400 {
    @apply text-gray-900;
  }

  /* Dark backgrounds get white text */
  .avatar.bg-red-500,
  .avatar.bg-red-600,
  .avatar.bg-red-700,
  .avatar.bg-orange-500,
  .avatar.bg-orange-600,
  .avatar.bg-orange-700,
  .avatar.bg-amber-500,
  .avatar.bg-amber-600,
  .avatar.bg-amber-700,
  .avatar.bg-yellow-600,
  .avatar.bg-yellow-700,
  .avatar.bg-lime-500,
  .avatar.bg-lime-600,
  .avatar.bg-lime-700,
  .avatar.bg-green-500,
  .avatar.bg-green-600,
  .avatar.bg-green-700,
  .avatar.bg-emerald-500,
  .avatar.bg-emerald-600,
  .avatar.bg-emerald-700,
  .avatar.bg-teal-500,
  .avatar.bg-teal-600,
  .avatar.bg-teal-700,
  .avatar.bg-cyan-500,
  .avatar.bg-cyan-600,
  .avatar.bg-cyan-700,
  .avatar.bg-sky-500,
  .avatar.bg-sky-600,
  .avatar.bg-sky-700,
  .avatar.bg-blue-500,
  .avatar.bg-blue-600,
  .avatar.bg-blue-700,
  .avatar.bg-indigo-500,
  .avatar.bg-indigo-600,
  .avatar.bg-indigo-700,
  .avatar.bg-violet-500,
  .avatar.bg-violet-600,
  .avatar.bg-violet-700,
  .avatar.bg-purple-500,
  .avatar.bg-purple-600,
  .avatar.bg-purple-700,
  .avatar.bg-fuchsia-500,
  .avatar.bg-fuchsia-600,
  .avatar.bg-fuchsia-700,
  .avatar.bg-pink-500,
  .avatar.bg-pink-600,
  .avatar.bg-pink-700,
  .avatar.bg-rose-500,
  .avatar.bg-rose-600,
  .avatar.bg-rose-700,
  .avatar.bg-gray-500,
  .avatar.bg-gray-600,
  .avatar.bg-gray-700,
  .avatar.bg-gray-800,
  .avatar.bg-gray-900,
  .avatar.bg-slate-500,
  .avatar.bg-slate-600,
  .avatar.bg-slate-700,
  .avatar.bg-slate-800,
  .avatar.bg-slate-900 {
    @apply text-white;
  }
}
