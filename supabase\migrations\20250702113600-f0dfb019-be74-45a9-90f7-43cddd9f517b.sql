
-- Remove all performance data for the specified agents
DELETE FROM agent_performance 
WHERE agent_id IN (
  SELECT id FROM agents 
  WHERE name IN (
    '<PERSON><PERSON><PERSON><PERSON>egi',
    '<PERSON><PERSON> kambhoj', 
    '<PERSON><PERSON><PERSON> kukreti',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>'
  )
);

-- Remove all sync logs for the specified agents
DELETE FROM sync_logs 
WHERE agent_name IN (
  '<PERSON>bha<PERSON><PERSON> Negi',
  '<PERSON><PERSON> kambhoj', 
  '<PERSON><PERSON><PERSON> kukreti',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON> T<PERSON>pa',
  '<PERSON><PERSON><PERSON>'
);

-- Finally, remove the agents themselves
DELETE FROM agents 
WHERE name IN (
  '<PERSON><PERSON><PERSON><PERSON> Negi',
  '<PERSON><PERSON> kambhoj', 
  '<PERSON><PERSON><PERSON> kukreti',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>bhang<PERSON> Thapa',
  '<PERSON><PERSON><PERSON>'
);
