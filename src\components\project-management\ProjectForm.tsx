
import React from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";

type ProjectFormProps = {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
};

type ProjectFormData = {
  name: string;
  code: string;
};

const ProjectForm: React.FC<ProjectFormProps> = ({ isOpen, setIsOpen }) => {
  const { addProject, getRandomColor } = useDashboard();
  const { register, handleSubmit, formState: { errors }, reset } = useForm<ProjectFormData>();

  const onSubmit = async (data: ProjectFormData) => {
    // Create a new project with the form data
    const newProject = {
      name: data.name,
      code: data.code.toUpperCase(),
      color: getRandomColor(),
    };

    const success = await addProject(newProject);
    if (success) {
      setIsOpen(false);
      reset();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add New Project</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Project Name</Label>
            <Input
              id="name"
              placeholder="Enter project name"
              {...register("name", { required: "Project name is required" })}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="code">Project Code (2-3 characters)</Label>
            <Input
              id="code"
              placeholder="Enter project code"
              maxLength={3}
              {...register("code", {
                required: "Project code is required",
                maxLength: {
                  value: 3,
                  message: "Code cannot exceed 3 characters",
                },
                minLength: {
                  value: 2,
                  message: "Code must be at least 2 characters",
                },
              })}
            />
            {errors.code && (
              <p className="text-sm text-red-500">{errors.code.message}</p>
            )}
          </div>
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button type="submit">Add Project</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ProjectForm;
