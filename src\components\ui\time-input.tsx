
import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

interface TimeInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const TimeInput: React.FC<TimeInputProps> = ({
  value,
  onChange,
  placeholder = "00:00:00",
  className,
  disabled = false,
}) => {
  const [inputValue, setInputValue] = useState(value);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  const formatTimeInput = (input: string): string => {
    // Remove any non-digit characters
    const digits = input.replace(/\D/g, '');
    
    // Limit to 6 digits (HHMMSS)
    const limitedDigits = digits.slice(0, 6);
    
    // Pad with zeros if needed
    const paddedDigits = limitedDigits.padStart(6, '0');
    
    // Format as HH:MM:SS
    const hours = paddedDigits.slice(0, 2);
    const minutes = paddedDigits.slice(2, 4);
    const seconds = paddedDigits.slice(4, 6);
    
    return `${hours}:${minutes}:${seconds}`;
  };

  const validateTime = (timeString: string): boolean => {
    const parts = timeString.split(':');
    if (parts.length !== 3) return false;

    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    const seconds = parseInt(parts[2], 10);

    // Enhanced validation - allow more flexible ranges for talk time tracking
    return (
      !isNaN(hours) && hours >= 0 && hours <= 99 && // Allow up to 99 hours for long sessions
      !isNaN(minutes) && minutes >= 0 && minutes <= 59 &&
      !isNaN(seconds) && seconds >= 0 && seconds <= 59
    );
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Allow backspace and delete - be more permissive during editing
    if (newValue.length < inputValue.length) {
      setInputValue(newValue);
      // Don't validate during deletion, just pass the value
      onChange(newValue);
      return;
    }

    // Allow direct typing with colons (e.g., "00:45:30")
    if (newValue.includes(':')) {
      setInputValue(newValue);
      // Validate and format only if it looks complete
      if (newValue.split(':').length === 3 && validateTime(newValue)) {
        onChange(newValue);
      } else {
        onChange(newValue); // Pass through for partial input
      }
      return;
    }

    // Format the input for numeric-only entry
    const formatted = formatTimeInput(newValue);
    setInputValue(formatted);
    onChange(formatted);
  };

  const handleBlur = () => {
    // Ensure proper formatting on blur
    if (inputValue && !validateTime(inputValue)) {
      const formatted = formatTimeInput(inputValue);
      setInputValue(formatted);
      onChange(formatted);
    }
  };

  return (
    <Input
      type="text"
      value={inputValue}
      onChange={handleInputChange}
      onBlur={handleBlur}
      placeholder={placeholder}
      className={cn("font-mono", className)}
      disabled={disabled}
      maxLength={8}
    />
  );
};
