import { format, isWithinInterval, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, addWeeks, isSameDay } from "date-fns";
import { downloadCSV } from "./csvUtils";
import { Project, Agent, WeekFrame, TimeFrame } from "@/types/dashboard";
import { AgentPerformance } from "@/hooks/dashboard/useAgentPerformanceOperations";
import {
  calculateProjectTargetSuccessRate,
  calculateAgentTargetSuccessRate,
  formatTargetSuccessRate,
  getColorCodeText
} from "@/utils/targetSuccessRateUtils";

export type ExportScope = "all-projects" | "project-wise" | "agent-wise" | "current-view" | "specific-project";

export interface ExportOptions {
  scope: ExportScope;
  timeFrame: string;
  selectedDate?: Date;
  selectedWeek?: WeekFrame | null;
  selectedMonth?: string;
  selectedProject?: string;
  selectedAgent?: string | null;
  specificProjectId?: string; // For specific project detail export
  startDate?: Date; // For AI-generated date ranges
  endDate?: Date; // For AI-generated date ranges
}

// Helper function to filter performance data based on selected date/week/month filters
const filterPerformanceDataByTimeFrame = (
  performanceData: AgentPerformance[],
  options: ExportOptions
): AgentPerformance[] => {
  return performanceData.filter(item => {
    const itemDate = new Date(item.date);

    // Handle AI-generated date ranges first
    if (options.startDate && options.endDate) {
      const startDate = startOfDay(options.startDate);
      const endDate = endOfDay(options.endDate);
      return isWithinInterval(itemDate, { start: startDate, end: endDate });
    }

    switch (options.timeFrame) {
      case "Daily":
        if (options.selectedDate) {
          return isSameDay(itemDate, options.selectedDate);
        }
        return true;

      case "Weekly":
        if (options.selectedWeek && options.selectedMonth) {
          // Helper function to get week date range from WeekFrame
          const getWeekDateRange = (weekFrame: string) => {
            const monthStart = startOfMonth(new Date());
            const weekNumber = parseInt(weekFrame.replace('Week ', '')) - 1;
            const weekStart = addWeeks(startOfWeek(monthStart), weekNumber);
            const weekEnd = endOfWeek(weekStart);
            return { weekStart, weekEnd };
          };

          const { weekStart, weekEnd } = getWeekDateRange(options.selectedWeek);
          return isWithinInterval(itemDate, {
            start: startOfDay(weekStart),
            end: endOfDay(weekEnd)
          });
        }
        return true;

      case "Monthly":
        if (options.selectedMonth && options.selectedMonth !== "All Time") {
          const itemMonthName = format(itemDate, 'MMM');
          return itemMonthName === options.selectedMonth;
        }
        return true;

      default:
        return true;
    }
  });
};

// Helper function to generate dynamic time frame text
const generateTimeFrameText = (options: ExportOptions): string => {
  // Handle AI-generated date ranges
  if (options.startDate && options.endDate) {
    const startStr = format(options.startDate, 'd-M-yyyy');
    const endStr = format(options.endDate, 'd-M-yyyy');
    return startStr === endStr ? startStr : `${startStr} to ${endStr}`;
  }

  switch (options.timeFrame) {
    case "Daily":
      if (options.selectedDate) {
        return format(options.selectedDate, 'd-M-yyyy');
      }
      return "Daily";

    case "Weekly":
      // For weekly filter, show only the week (e.g., "Week 1", "Week 2")
      return options.selectedWeek || "Weekly";

    case "Monthly":
      // For monthly filter, show only the month (e.g., "June", "July")
      if (options.selectedMonth && options.selectedMonth !== "All Time") {
        return options.selectedMonth;
      }
      return "Monthly";

    default:
      return options.timeFrame;
  }
};

// Format talk time from seconds to HH:MM:SS
const formatTalkTimeForExport = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// Generate filename based on export options
const generateFilename = (options: ExportOptions): string => {
  const timestamp = format(new Date(), "yyyy-MM-dd_HH-mm-ss");
  const scope = options.scope.replace("-", "_");
  const timeFrame = options.timeFrame.toLowerCase();

  let filename = `performance_export_${scope}_${timeFrame}_${timestamp}`;

  if (options.selectedProject && options.selectedProject !== "All") {
    filename += `_${options.selectedProject.replace(/\s+/g, "_")}`;
  }

  if (options.selectedAgent) {
    filename += `_agent`;
  }

  if (options.selectedWeek) {
    filename += `_${options.selectedWeek.replace(/\s+/g, "_").toLowerCase()}`;
  }

  return `${filename}.csv`;
};

// Export All Projects Summary
export const exportAllProjectsSummary = (
  projects: Project[],
  agents: Agent[],
  performanceData: AgentPerformance[],
  options: ExportOptions
): void => {
  // Apply date filtering first
  const filteredData = filterPerformanceDataByTimeFrame(performanceData, options);

  const headers = [
    "Project Name",
    "Dials",
    "Connected",
    "Talk Time",
    "Scheduled",
    "Successful",
    "Target Success Rate",
    "Color Code",
    "Over Achieved",
    "FOP Scheduled",
    "FOP Successful",
    "Time Frame"
  ];

  const rows = projects
    .filter(project => project.id !== "all")
    .map(project => {
      const projectData = filteredData.filter(item => item.project_id === project.id);
      const projectAgents = agents.filter(agent => agent.projectId === project.id);

      const totalDials = projectData.reduce((sum, item) => sum + item.dials, 0);
      const totalConnected = projectData.reduce((sum, item) => sum + item.connected, 0);
      const totalTalkTime = projectData.reduce((sum, item) => sum + item.talk_time, 0);
      const totalScheduled = projectData.reduce((sum, item) => sum + item.scheduled_meetings, 0);
      const totalSuccessful = projectData.reduce((sum, item) => sum + item.successful_meetings, 0);
      const totalFopScheduled = projectData.reduce((sum, item) => sum + (item.fop_scheduled || 0), 0);
      const totalFopSuccessful = projectData.reduce((sum, item) => sum + (item.fop_successful || 0), 0);

      // Calculate target success rate
      const targetSuccessRateData = calculateProjectTargetSuccessRate(
        totalSuccessful,
        options.timeFrame as TimeFrame,
        projectAgents
      );

      return [
        project.name,
        totalDials.toString(),
        totalConnected.toString(),
        formatTalkTimeForExport(totalTalkTime),
        totalScheduled.toString(),
        totalSuccessful.toString(),
        formatTargetSuccessRate(targetSuccessRateData.targetSuccessRate),
        getColorCodeText(targetSuccessRateData.targetSuccessRate),
        targetSuccessRateData.isOverAchieved ? "Yes" : "No",
        totalFopScheduled.toString(),
        totalFopSuccessful.toString(),
        generateTimeFrameText(options)
      ];
    });

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(","))
    .join("\n");

  const filename = generateFilename(options);
  downloadCSV(filename, csvContent);
};

// Export Project-wise Detailed (All agents under each project)
export const exportProjectWiseDetailed = (
  projects: Project[],
  agents: Agent[],
  performanceData: AgentPerformance[],
  options: ExportOptions
): void => {
  // Apply date filtering first
  const filteredData = filterPerformanceDataByTimeFrame(performanceData, options);

  const headers = [
    "Project Name",
    "Agent Name",
    "Date",
    "Dials",
    "Connected",
    "Talk Time",
    "Scheduled",
    "Successful",
    "Target Success Rate",
    "Color Code",
    "Over Achieved",
    "FOP Scheduled",
    "FOP Successful",
    "Time Frame"
  ];

  const rows: string[][] = [];

  projects
    .filter(project => project.id !== "all")
    .forEach(project => {
      const projectAgents = agents.filter(agent => agent.projectId === project.id);

      projectAgents.forEach(agent => {
        const agentData = filteredData.filter(item => item.agent_id === agent.id);

        if (agentData.length === 0) {
          // Add a row even if no data exists for the filtered period
          const targetSuccessRateData = calculateAgentTargetSuccessRate(0, options.timeFrame as TimeFrame);

          rows.push([
            project.name,
            agent.name,
            "No Data",
            "0",
            "0",
            "00:00:00",
            "0",
            "0",
            formatTargetSuccessRate(targetSuccessRateData.targetSuccessRate),
            getColorCodeText(targetSuccessRateData.targetSuccessRate),
            targetSuccessRateData.isOverAchieved ? "Yes" : "No",
            "0",
            "0",
            generateTimeFrameText(options)
          ]);
        } else {
          agentData.forEach(item => {
            const targetSuccessRateData = calculateAgentTargetSuccessRate(
              item.successful_meetings,
              options.timeFrame as TimeFrame
            );

            rows.push([
              project.name,
              agent.name,
              item.date,
              item.dials.toString(),
              item.connected.toString(),
              formatTalkTimeForExport(item.talk_time),
              item.scheduled_meetings.toString(),
              item.successful_meetings.toString(),
              formatTargetSuccessRate(targetSuccessRateData.targetSuccessRate),
              getColorCodeText(targetSuccessRateData.targetSuccessRate),
              targetSuccessRateData.isOverAchieved ? "Yes" : "No",
              (item.fop_scheduled || 0).toString(),
              (item.fop_successful || 0).toString(),
              generateTimeFrameText(options)
            ]);
          });
        }
      });
    });

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(","))
    .join("\n");

  const filename = generateFilename(options);
  downloadCSV(filename, csvContent);
};

// Export Agent-wise (All agents across projects)
export const exportAgentWise = (
  projects: Project[],
  agents: Agent[],
  performanceData: AgentPerformance[],
  options: ExportOptions
): void => {
  // Apply date filtering first
  const filteredData = filterPerformanceDataByTimeFrame(performanceData, options);

  const headers = [
    "Agent Name",
    "Project Name",
    "Total Dials",
    "Total Connected",
    "Total Talk Time",
    "Total Scheduled",
    "Total Successful",
    "Target Success Rate",
    "Color Code",
    "Over Achieved",
    "Total FOP Scheduled",
    "Total FOP Successful",
    "Time Frame"
  ];

  const rows = agents.map(agent => {
    const project = projects.find(p => p.id === agent.projectId);
    const agentData = filteredData.filter(item => item.agent_id === agent.id);

    const totalDials = agentData.reduce((sum, item) => sum + item.dials, 0);
    const totalConnected = agentData.reduce((sum, item) => sum + item.connected, 0);
    const totalTalkTime = agentData.reduce((sum, item) => sum + item.talk_time, 0);
    const totalScheduled = agentData.reduce((sum, item) => sum + item.scheduled_meetings, 0);
    const totalSuccessful = agentData.reduce((sum, item) => sum + item.successful_meetings, 0);
    const totalFopScheduled = agentData.reduce((sum, item) => sum + (item.fop_scheduled || 0), 0);
    const totalFopSuccessful = agentData.reduce((sum, item) => sum + (item.fop_successful || 0), 0);

    // Calculate target success rate
    const targetSuccessRateData = calculateAgentTargetSuccessRate(
      totalSuccessful,
      options.timeFrame as TimeFrame
    );

    return [
      agent.name,
      project?.name || "Unknown Project",
      totalDials.toString(),
      totalConnected.toString(),
      formatTalkTimeForExport(totalTalkTime),
      totalScheduled.toString(),
      totalSuccessful.toString(),
      formatTargetSuccessRate(targetSuccessRateData.targetSuccessRate),
      getColorCodeText(targetSuccessRateData.targetSuccessRate),
      targetSuccessRateData.isOverAchieved ? "Yes" : "No",
      totalFopScheduled.toString(),
      totalFopSuccessful.toString(),
      generateTimeFrameText(options)
    ];
  });

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(","))
    .join("\n");

  const filename = generateFilename(options);
  downloadCSV(filename, csvContent);
};

// Export Specific Project Detail
export const exportSpecificProjectDetail = (
  projects: Project[],
  agents: Agent[],
  performanceData: AgentPerformance[],
  options: ExportOptions
): void => {
  if (!options.specificProjectId) {
    throw new Error("Specific project ID is required for specific project export");
  }

  const project = projects.find(p => p.id === options.specificProjectId);
  if (!project) {
    throw new Error("Project not found");
  }

  // Use the existing project-wise detailed export but for a single project
  // The filtering will be applied within exportProjectWiseDetailed
  exportProjectWiseDetailed(
    [project],
    agents.filter(a => a.projectId === options.specificProjectId),
    performanceData.filter(item => item.project_id === options.specificProjectId),
    { ...options, selectedProject: options.specificProjectId }
  );
};

// Export Current View (based on current filters and selection)
export const exportCurrentView = (
  projects: Project[],
  agents: Agent[],
  performanceData: AgentPerformance[],
  options: ExportOptions
): void => {
  if (options.selectedAgent) {
    // Export single agent detailed data with date filtering
    const agent = agents.find(a => a.id === options.selectedAgent);
    const project = projects.find(p => p.id === agent?.projectId);
    const filteredData = filterPerformanceDataByTimeFrame(performanceData, options);
    const agentData = filteredData.filter(item => item.agent_id === options.selectedAgent);

    const headers = [
      "Project Name",
      "Agent Name",
      "Date",
      "Dials",
      "Connected",
      "Talk Time",
      "Scheduled",
      "Successful",
      "Target Success Rate",
      "Color Code",
      "Over Achieved",
      "FOP Scheduled",
      "FOP Successful",
      "Time Frame"
    ];

    const rows = agentData.map(item => {
      const targetSuccessRateData = calculateAgentTargetSuccessRate(
        item.successful_meetings,
        options.timeFrame as TimeFrame
      );

      return [
        project?.name || "Unknown Project",
        agent?.name || "Unknown Agent",
        item.date,
        item.dials.toString(),
        item.connected.toString(),
        formatTalkTimeForExport(item.talk_time),
        item.scheduled_meetings.toString(),
        item.successful_meetings.toString(),
        formatTargetSuccessRate(targetSuccessRateData.targetSuccessRate),
        getColorCodeText(targetSuccessRateData.targetSuccessRate),
        targetSuccessRateData.isOverAchieved ? "Yes" : "No",
        (item.fop_scheduled || 0).toString(),
        (item.fop_successful || 0).toString(),
        generateTimeFrameText(options)
      ];
    });

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(","))
      .join("\n");

    const filename = generateFilename(options);
    downloadCSV(filename, csvContent);

  } else if (options.selectedProject && options.selectedProject !== "All") {
    // Export single project with all its agents (filtering applied within exportProjectWiseDetailed)
    exportProjectWiseDetailed(
      projects.filter(p => p.id === options.selectedProject),
      agents.filter(a => a.projectId === options.selectedProject),
      performanceData.filter(item => item.project_id === options.selectedProject),
      options
    );
  } else {
    // Export all projects summary (filtering applied within exportAllProjectsSummary)
    exportAllProjectsSummary(projects, agents, performanceData, options);
  }
};
