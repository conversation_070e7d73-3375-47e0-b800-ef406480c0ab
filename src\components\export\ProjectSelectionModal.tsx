import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Project } from "@/types/dashboard";
import { useDashboard } from "@/contexts/DashboardContext";

interface ProjectSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (projectId: string) => void;
  projects: Project[];
}

export const ProjectSelectionModal: React.FC<ProjectSelectionModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  projects
}) => {
  const [selectedProjectId, setSelectedProjectId] = useState<string>("");
  const { timeFrame, selectedDate, selectedWeek, selectedMonth } = useDashboard();

  const handleConfirm = () => {
    if (selectedProjectId) {
      onConfirm(selectedProjectId);
      setSelectedProjectId("");
      onClose();
    }
  };

  const handleClose = () => {
    setSelectedProjectId("");
    onClose();
  };

  const getFilterDescription = () => {
    if (timeFrame === "Daily" && selectedDate) {
      return `Daily view for ${selectedDate.toLocaleDateString()}`;
    } else if (timeFrame === "Weekly" && selectedWeek) {
      return `Weekly view for ${selectedWeek}`;
    } else if (timeFrame === "Monthly" && selectedMonth) {
      return `Monthly view for ${selectedMonth}`;
    }
    return `${timeFrame} view`;
  };

  const selectedProject = projects.find(p => p.id === selectedProjectId);

  // Filter out "All" project
  const availableProjects = projects.filter(project => project.id !== "all");

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Select Project for Export</DialogTitle>
          <DialogDescription>
            Choose a specific project to export detailed performance data.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Project
            </label>
            <Select value={selectedProjectId} onValueChange={setSelectedProjectId}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a project..." />
              </SelectTrigger>
              <SelectContent>
                {availableProjects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    <div className="flex items-center gap-2">
                      <div className={`avatar ${project.color} w-6 h-6 text-xs`}>
                        {project.code}
                      </div>
                      <span>{project.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedProject && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="text-sm">
                <div className="font-medium text-blue-800 mb-1">Export Preview:</div>
                <div className="text-blue-700">
                  <strong>{selectedProject.name}</strong> data for {getFilterDescription()}
                </div>
                <div className="text-xs text-blue-600 mt-1">
                  This will include all agents and their performance data for the selected project.
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleConfirm} 
            disabled={!selectedProjectId}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Export {selectedProject?.name || "Project"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProjectSelectionModal;
