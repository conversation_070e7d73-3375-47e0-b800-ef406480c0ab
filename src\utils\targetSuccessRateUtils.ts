import { TimeFrame, TargetSuccessRateData, TargetConfig, Agent } from "@/types/dashboard";

// Target configuration as per requirements
export const AGENT_TARGET_CONFIG: TargetConfig = {
  daily: 1,
  weekly: 5,
  monthly: 20
};

/**
 * Calculate target meetings based on time frame and number of agents
 */
export const calculateTargetMeetings = (
  timeFrame: TimeFrame,
  agentCount: number = 1
): number => {
  switch (timeFrame) {
    case "Daily":
      return AGENT_TARGET_CONFIG.daily * agentCount;
    case "Weekly":
      return AGENT_TARGET_CONFIG.weekly * agentCount;
    case "Monthly":
      return AGENT_TARGET_CONFIG.monthly * agentCount;
    default:
      // For other time frames, default to monthly
      return AGENT_TARGET_CONFIG.monthly * agentCount;
  }
};

/**
 * Calculate target success rate percentage
 */
export const calculateTargetSuccessRate = (
  actualSuccessful: number,
  targetMeetings: number
): number => {
  if (targetMeetings === 0) return 0;
  return (actualSuccessful / targetMeetings) * 100;
};

/**
 * Determine color code based on target success rate
 */
export const getTargetSuccessRateColor = (
  targetSuccessRate: number
): 'red' | 'yellow' | 'green' | 'dark-green' => {
  if (targetSuccessRate > 100) return 'dark-green';
  if (targetSuccessRate >= 99) return 'green';
  if (targetSuccessRate >= 75) return 'yellow';
  return 'red';
};

/**
 * Check if target is over-achieved (above 100%)
 */
export const isTargetOverAchieved = (targetSuccessRate: number): boolean => {
  return targetSuccessRate > 100;
};

/**
 * Get CSS classes for target success rate display
 */
export const getTargetSuccessRateClasses = (
  targetSuccessRate: number
): string => {
  const color = getTargetSuccessRateColor(targetSuccessRate);
  
  switch (color) {
    case 'dark-green':
      return 'text-green-800 font-semibold';
    case 'green':
      return 'text-green-600';
    case 'yellow':
      return 'text-amber-500';
    case 'red':
      return 'text-red-500';
    default:
      return 'text-gray-500';
  }
};

/**
 * Get background color classes for progress bars
 */
export const getTargetSuccessRateProgressColor = (
  targetSuccessRate: number
): string => {
  const color = getTargetSuccessRateColor(targetSuccessRate);
  
  switch (color) {
    case 'dark-green':
      return 'bg-green-800';
    case 'green':
      return 'bg-green-500';
    case 'yellow':
      return 'bg-amber-500';
    case 'red':
      return 'bg-red-500';
    default:
      return 'bg-gray-400';
  }
};

/**
 * Calculate complete target success rate data for an agent
 */
export const calculateAgentTargetSuccessRate = (
  actualSuccessful: number,
  timeFrame: TimeFrame
): TargetSuccessRateData => {
  const targetMeetings = calculateTargetMeetings(timeFrame, 1);
  const targetSuccessRate = calculateTargetSuccessRate(actualSuccessful, targetMeetings);
  
  return {
    targetSuccessRate,
    colorCode: getTargetSuccessRateColor(targetSuccessRate),
    isOverAchieved: isTargetOverAchieved(targetSuccessRate),
    actualSuccessful,
    targetMeetings
  };
};

/**
 * Calculate complete target success rate data for a project
 */
export const calculateProjectTargetSuccessRate = (
  actualSuccessful: number,
  timeFrame: TimeFrame,
  agents: Agent[]
): TargetSuccessRateData => {
  const agentCount = agents.length;
  const targetMeetings = calculateTargetMeetings(timeFrame, agentCount);
  const targetSuccessRate = calculateTargetSuccessRate(actualSuccessful, targetMeetings);
  
  return {
    targetSuccessRate,
    colorCode: getTargetSuccessRateColor(targetSuccessRate),
    isOverAchieved: isTargetOverAchieved(targetSuccessRate),
    actualSuccessful,
    targetMeetings
  };
};

/**
 * Format target success rate for display
 */
export const formatTargetSuccessRate = (targetSuccessRate: number): string => {
  return `${targetSuccessRate.toFixed(1)}%`;
};

/**
 * Get color code text for exports
 */
export const getColorCodeText = (
  targetSuccessRate: number
): string => {
  const color = getTargetSuccessRateColor(targetSuccessRate);
  
  switch (color) {
    case 'dark-green':
      return 'Dark Green';
    case 'green':
      return 'Green';
    case 'yellow':
      return 'Yellow';
    case 'red':
      return 'Red';
    default:
      return 'Gray';
  }
};
