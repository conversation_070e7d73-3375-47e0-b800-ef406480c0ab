
import React from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTit<PERSON> } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";
import { Project } from "@/types/dashboard";
import InlineEdit from "@/components/ui/inline-edit";

type AgentPanelProps = {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  project?: Project | null;
};

const AgentPanel: React.FC<AgentPanelProps> = ({ isOpen, setIsOpen, project }) => {
  const {
    setIsNewAgentModalOpen,
    getAgentsByProjectId,
    updateAgent,
    deleteAgent,
    getAgentInitials,
    getRandomColor,
    isLoading
  } = useDashboard();

  const handleAddAgent = () => {
    setIsOpen(false);
    setIsNewAgentModalOpen(true);
  };

  const handleDeleteAgent = async (agentId: string) => {
    if (confirm("Are you sure you want to remove this agent?")) {
      await deleteAgent(agentId);
    }
  };

  const handleUpdateAgentName = async (agentId: string, newName: string) => {
    return await updateAgent(agentId, { name: newName });
  };

  const projectAgents = project ? getAgentsByProjectId(project.id) : [];

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetContent className="w-full sm:max-w-md">
        <SheetHeader>
          <SheetTitle>
            {project ? `${project.name} Agents` : "Project Agents"}
          </SheetTitle>
        </SheetHeader>
        
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin h-8 w-8 border-4 border-amplior-primary border-t-transparent rounded-full"></div>
          </div>
        ) : (
          <>
            {project && (
              <div className="mt-6">
                <Button onClick={handleAddAgent} className="w-full">
                  <Plus className="mr-2 h-4 w-4" /> Add Agent to {project.name}
                </Button>
              </div>
            )}

            <div className="mt-6">
              <h3 className="font-medium mb-4">
                {project ? `${project.name} Agents (${projectAgents.length})` : "No Project Selected"}
              </h3>
              
              {project && projectAgents.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  No agents assigned to this project yet.
                </div>
              ) : (
                <div className="space-y-2">
                  {projectAgents.map((agent) => (
                    <div key={agent.id} className="flex items-center p-3 bg-gray-50 rounded-md">
                      <div className={`${getRandomColor()} w-8 h-8 rounded-full flex items-center justify-center text-white mr-3`}>
                        {getAgentInitials(agent.name)}
                      </div>
                      <InlineEdit
                        value={agent.name}
                        onSave={(newName) => handleUpdateAgentName(agent.id, newName)}
                        variant="default"
                        placeholder="Enter agent name"
                        maxLength={50}
                        className="flex-1"
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50"
                        onClick={() => handleDeleteAgent(agent.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </>
        )}
      </SheetContent>
    </Sheet>
  );
};

export default AgentPanel;
