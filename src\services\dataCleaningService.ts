
const GEMINI_API_KEY = "AIzaSyArr9TLQHEBgTbrs4UDe0iGDdCaCicbXfQ";
const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";

export interface CleanedDataRow {
  date: string;
  project_name: string;
  manager_name: string;
  agent_name: string;
  total_dials: number;
  connected_calls: number;
  talk_time: string;
  scheduled_meetings: number;
  successful_meetings: number;
  error_message: string;
}

export const cleanAndFormatData = async (rawData: string): Promise<CleanedDataRow[]> => {
  try {
    const prompt = `
You are an expert data cleaning AI for a sales performance tracking system. Clean and structure the following raw agent performance data:

INSTRUCTIONS:
1. Extract performance data from the raw input
2. Focus on finding these key metrics per row:
   - Date (convert to YYYY-MM-DD format)
   - Project name (DTSS, Rare, SIS 2.0, Ufirm, HungerBox, SIS NAG, etc.)
   - Manager name (if mentioned, otherwise use "Manager")
   - Agent name (full name)
   - Total dials (number of calls made)
   - Connected calls (calls that connected)
   - Talk time (convert to HH:MM:SS format)
   - Scheduled meetings (meetings scheduled)
   - Successful meetings (meetings that were successful)

3. Data Parsing Rules:
   - Ignore unrelated tokens like "Week 4", "May", etc.
   - Convert dates like "05/22/2025" to "2025-05-22"
   - Convert time formats like "0:20:25" to "00:20:25"
   - Extract numbers from contexts like "Dials: 55" or just "55"
   - Count meeting numbers from patterns like "1(DTSS) 1(SIS)" = 2 meetings
   - Calculate totals when multiple entries exist for same agent/date

4. Quality Control:
   - Skip incomplete rows (missing essential data)
   - Set error_message for any validation issues
   - Ensure all numeric fields are valid integers
   - Ensure dates are valid and in correct format

5. Output Format:
   Return ONLY a JSON array with objects containing these exact fields:
   {
     "date": "2025-05-22",
     "project_name": "DTSS",
     "manager_name": "Manager",
     "agent_name": "Shivani Panwar",
     "total_dials": 55,
     "connected_calls": 23,
     "talk_time": "00:20:25",
     "scheduled_meetings": 12,
     "successful_meetings": 8,
     "error_message": ""
   }

Raw data to process:
${rawData}

Return ONLY the JSON array, no explanations or markdown formatting.
`;

    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [{ text: prompt }]
          }
        ],
        generationConfig: {
          temperature: 0.1,
          topP: 0.8,
          topK: 10,
          maxOutputTokens: 4096,
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Gemini API error:", errorText);
      throw new Error(`Gemini API failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error("Invalid response structure from Gemini API");
    }
    
    const cleanedResponse = data.candidates[0].content.parts[0].text;
    
    // Clean up the response - remove markdown code blocks
    const jsonString = cleanedResponse.replace(/```json\n?|```\n?|```/g, '').trim();
    
    try {
      const parsedData = JSON.parse(jsonString);
      const result = Array.isArray(parsedData) ? parsedData : [parsedData];
      
      // Validate and clean the parsed data
      return result.map((item: any) => ({
        date: item.date || "",
        project_name: item.project_name || "",
        manager_name: item.manager_name || "Manager",
        agent_name: item.agent_name || "",
        total_dials: parseInt(item.total_dials) || 0,
        connected_calls: parseInt(item.connected_calls) || 0,
        talk_time: item.talk_time || "00:00:00",
        scheduled_meetings: parseInt(item.scheduled_meetings) || 0,
        successful_meetings: parseInt(item.successful_meetings) || 0,
        error_message: item.error_message || ""
      }));
    } catch (parseError) {
      console.error("Error parsing JSON response:", parseError);
      console.error("Raw response:", cleanedResponse);
      
      // Fallback: try to manually parse the data if JSON parsing fails
      return fallbackDataParsing(rawData);
    }
  } catch (error) {
    console.error("Error cleaning data with Gemini API:", error);
    
    // Fallback to manual parsing
    return fallbackDataParsing(rawData);
  }
};

// Enhanced fallback manual parsing function
const fallbackDataParsing = (rawData: string): CleanedDataRow[] => {
  const lines = rawData.trim().split('\n').filter(line => line.trim());
  const results: CleanedDataRow[] = [];
  
  for (const line of lines) {
    try {
      // Skip lines that look like headers or context
      if (line.toLowerCase().includes('project:') || 
          line.toLowerCase().includes('agent:') ||
          line.toLowerCase().includes('data:')) {
        continue;
      }
      
      // Split by multiple spaces or tabs to get columns
      const parts = line.trim().split(/\s+/);
      
      if (parts.length < 5) {
        continue; // Skip incomplete lines
      }
      
      // Try to extract date (first recognizable date pattern)
      let date = "";
      let dateIdx = -1;
      for (let i = 0; i < parts.length; i++) {
        if (parts[i].match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
          const dateParts = parts[i].split('/');
          const month = dateParts[0].padStart(2, '0');
          const day = dateParts[1].padStart(2, '0');
          const year = dateParts[2];
          date = `${year}-${month}-${day}`;
          dateIdx = i;
          break;
        }
      }
      
      // Find project names
      const knownProjects = ['DTSS', 'Rare', 'SIS', 'Ufirm', 'HungerBox'];
      let project_name = "";
      for (const part of parts) {
        if (knownProjects.some(p => part.includes(p))) {
          project_name = part;
          break;
        }
      }
      
      // Find agent name (usually a combination of words with capitals)
      let agent_name = "";
      const nameWords = parts.filter(part => 
        /^[A-Z][a-z]+$/.test(part) && 
        !knownProjects.includes(part) &&
        !part.includes(':') &&
        isNaN(parseInt(part))
      );
      if (nameWords.length >= 2) {
        agent_name = nameWords.slice(0, 2).join(' ');
      }
      
      // Extract numbers (dials, connected, meetings)
      const numbers = parts.filter(p => /^\d+$/.test(p)).map(n => parseInt(n));
      
      // Find talk time
      let talk_time = "00:00:00";
      const timeMatch = parts.find(p => /\d+:\d+/.test(p));
      if (timeMatch) {
        const timeParts = timeMatch.split(':');
        if (timeParts.length === 2) {
          talk_time = `00:${timeParts[0].padStart(2, '0')}:${timeParts[1].padStart(2, '0')}`;
        } else if (timeParts.length === 3) {
          talk_time = `${timeParts[0].padStart(2, '0')}:${timeParts[1].padStart(2, '0')}:${timeParts[2].padStart(2, '0')}`;
        }
      }
      
      // Count meetings from parentheses
      const meetingMatches = line.match(/\d+\([^)]+\)/g) || [];
      const scheduled_meetings = meetingMatches.length;
      
      results.push({
        date,
        project_name,
        manager_name: "Manager",
        agent_name,
        total_dials: numbers[0] || 0,
        connected_calls: numbers[1] || 0,
        talk_time,
        scheduled_meetings,
        successful_meetings: scheduled_meetings, // Assume all scheduled are successful for fallback
        error_message: ""
      });
      
    } catch (error) {
      console.error("Error parsing line:", line, error);
      results.push({
        date: "",
        project_name: "",
        manager_name: "Manager",
        agent_name: "",
        total_dials: 0,
        connected_calls: 0,
        talk_time: "00:00:00",
        scheduled_meetings: 0,
        successful_meetings: 0,
        error_message: `Parse error: ${error.message}`
      });
    }
  }
  
  return results.filter(result => 
    result.date && result.agent_name && result.project_name
  );
};
