# Deployment Guide

This project is configured for automatic deployment to Vercel. Follow these steps to make your application live:

## Quick Deploy (Recommended)

### Option 1: Deploy with Vercel (One-Click)

1. **Visit Vercel**: Go to [vercel.com](https://vercel.com)
2. **Sign up/Login**: Use your GitHub account
3. **Import Project**: Click "New Project" → Import from GitHub
4. **Select Repository**: Choose `prashantsingh432/amplior-agent-compass`
5. **Configure Environment Variables**:
   - `VITE_SUPABASE_URL`: Your Supabase project URL
   - `VITE_SUPABASE_ANON_KEY`: Your Supabase anon key
6. **Deploy**: Click "Deploy" - Your app will be live in ~2 minutes!

### Option 2: Deploy with Netlify

1. **Visit Netlify**: Go to [netlify.com](https://netlify.com)
2. **Sign up/Login**: Use your GitHub account
3. **New Site**: Click "New site from Git"
4. **Connect GitHub**: Authorize and select your repository
5. **Build Settings**:
   - Build command: `npm run build`
   - Publish directory: `dist`
6. **Environment Variables**: Add your Supabase credentials
7. **Deploy**: Your site will be live!

## Manual Deployment Steps

### Prerequisites
- Node.js 18+ installed
- GitHub account
- Supabase project set up

### 1. Build the Project
```bash
npm install
npm run build
```

### 2. Environment Variables
Create a `.env` file with:
```
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. Deploy to Vercel CLI
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

## Environment Variables Required

| Variable | Description | Example |
|----------|-------------|---------|
| `VITE_SUPABASE_URL` | Your Supabase project URL | `https://xxx.supabase.co` |
| `VITE_SUPABASE_ANON_KEY` | Your Supabase anonymous key | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |

## Features Included in Deployment

✅ **Export Performance Feature**
- CSV export functionality
- Multiple export options (All Projects, Project-wise, Agent-wise, Current View)
- Progress indicators and toast notifications
- Smart filename generation

✅ **Real-time Dashboard**
- Live performance metrics
- Interactive charts and graphs
- Project and agent management

✅ **Data Entry System**
- Manual data entry forms
- Bulk CSV import
- AI-powered data cleaning

✅ **Responsive Design**
- Mobile-friendly interface
- Modern UI with Tailwind CSS
- Dark/light theme support

## Post-Deployment Checklist

1. ✅ Verify the application loads correctly
2. ✅ Test the Export Performance feature
3. ✅ Check Supabase connection
4. ✅ Test data entry functionality
5. ✅ Verify real-time updates work
6. ✅ Test on mobile devices

## Troubleshooting

### Build Errors
- Ensure all environment variables are set
- Check Node.js version (18+ required)
- Clear node_modules and reinstall: `rm -rf node_modules package-lock.json && npm install`

### Deployment Issues
- Verify GitHub repository is public or Vercel has access
- Check environment variables in deployment platform
- Review build logs for specific errors

### Runtime Errors
- Check browser console for JavaScript errors
- Verify Supabase credentials are correct
- Ensure Supabase RLS policies allow access

## Support

If you encounter any issues:
1. Check the deployment logs
2. Verify environment variables
3. Test locally first: `npm run dev`
4. Check Supabase dashboard for connection issues

## Live URL

Once deployed, your application will be available at:
- Vercel: `https://your-project-name.vercel.app`
- Netlify: `https://your-project-name.netlify.app`

The Export Performance feature will be accessible from:
- Project Performance Overview page (top right)
- Individual project/agent performance views
