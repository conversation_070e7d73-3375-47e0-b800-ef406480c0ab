
import React from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, TrendingUp, Clock, Phone, Calendar, CheckCircle, Users } from "lucide-react";
import { format, isWithinInterval, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, addWeeks } from "date-fns";

interface AgentTableProps {
  onAgentSelect?: (agentId: string) => void;
}

const AgentTable: React.FC<AgentTableProps> = ({ onAgentSelect }) => {
  const { 
    selectedProject, 
    getAgentsByProjectId, 
    formatTalkTime,
    getAgentInitials,
    performanceData,
    timeFrame,
    selectedDate,
    selectedWeek,
    selectedMonth
  } = useDashboard();

  const agents = getAgentsByProjectId(selectedProject);

  // Helper function to get week date range from WeekFrame
  const getWeekDateRange = (weekFrame: string) => {
    // Get the current month start
    const monthStart = startOfMonth(new Date());
    // Extract week number from "Week 1", "Week 2", etc.
    const weekNumber = parseInt(weekFrame.replace('Week ', '')) - 1;
    // Calculate the start of the specific week
    const weekStart = addWeeks(startOfWeek(monthStart), weekNumber);
    const weekEnd = endOfWeek(weekStart);
    
    return { weekStart, weekEnd };
  };

  // Filter performance data based on selected timeframe and date filters
  const getFilteredPerformanceData = () => {
    return performanceData.filter(item => {
      const itemDate = new Date(item.date);

      switch (timeFrame) {
        case "Daily":
          // For daily view, show data for the selected date
          return isWithinInterval(itemDate, {
            start: startOfDay(selectedDate),
            end: endOfDay(selectedDate)
          });

        case "Weekly":
          // For weekly view, show data for the selected week
          if (selectedWeek) {
            const { weekStart, weekEnd } = getWeekDateRange(selectedWeek);
            return isWithinInterval(itemDate, {
              start: startOfDay(weekStart),
              end: endOfDay(weekEnd)
            });
          }
          // If no week selected, show current week
          return isWithinInterval(itemDate, {
            start: startOfWeek(new Date()),
            end: endOfWeek(new Date())
          });

        case "Monthly":
          // For monthly view, show data for the selected month
          if (selectedMonth && selectedMonth !== "All Time") {
            // selectedMonth is in format "May", "Jun", etc.
            const itemMonthName = format(itemDate, 'MMM');
            return itemMonthName === selectedMonth;
          }
          // If "All Time" selected, show all data
          return true;

        default:
          return true;
      }
    });
  };

  const getAgentMetrics = (agentId: string) => {
    const filteredData = getFilteredPerformanceData();
    const agentData = filteredData.filter(item => item.agent_id === agentId);
    
    return {
      totalDials: agentData.reduce((sum, item) => sum + item.dials, 0),
      totalConnected: agentData.reduce((sum, item) => sum + item.connected, 0),
      totalTalkTime: agentData.reduce((sum, item) => sum + item.talk_time, 0),
      totalScheduled: agentData.reduce((sum, item) => sum + item.scheduled_meetings, 0),
      totalSuccessful: agentData.reduce((sum, item) => sum + item.successful_meetings, 0),
      totalFopScheduled: agentData.reduce((sum, item) => sum + (item.fop_scheduled || 0), 0),
      totalFopSuccessful: agentData.reduce((sum, item) => sum + (item.fop_successful || 0), 0),
      averageSuccessRate: agentData.length > 0 ? 
        agentData.reduce((sum, item) => sum + item.success_rate, 0) / agentData.length : 0
    };
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 70) return "text-green-600 bg-green-100";
    if (rate >= 50) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  // Get current filter description
  const getFilterDescription = () => {
    switch (timeFrame) {
      case "Daily":
        return `Daily view - ${format(selectedDate, "MMM dd, yyyy")}`;
      case "Weekly":
        return selectedWeek ? `Weekly view - ${selectedWeek}` : "Weekly view";
      case "Monthly":
        return selectedMonth === "All Time" ? "All Time view" : `Monthly view - ${selectedMonth} 2025`;
      default:
        return "All data";
    }
  };

  if (agents.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <TrendingUp className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">No agents found</h3>
          <p className="text-gray-500">There are no agents assigned to this project yet.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filter Status Header */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-blue-800">
              Agent Performance Overview
            </span>
          </div>
          <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-md">
            {getFilterDescription()} • Synced with filters • Real-time updates
          </span>
        </div>
      </div>

      {agents.map((agent) => {
        const metrics = getAgentMetrics(agent.id);
        const initials = getAgentInitials(agent.name);
        
        return (
          <Card key={agent.id} className="transition-all duration-200 hover:shadow-lg border border-gray-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                    {initials}
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg text-gray-900">{agent.name}</h3>
                    <p className="text-gray-500 text-sm">Agent ID: {agent.id.slice(-8).toUpperCase()}</p>
                  </div>
                </div>

                <div className="flex items-center gap-6">
                  {/* Performance Metrics */}
                  <div className="grid grid-cols-5 gap-4 text-center">
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-sm text-gray-500 mb-1">
                        <Phone className="w-3 h-3" />
                        <span>Dials</span>
                      </div>
                      <div className="font-semibold text-purple-600">{metrics.totalDials.toLocaleString()}</div>
                    </div>
                    
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-sm text-gray-500 mb-1">
                        <CheckCircle className="w-3 h-3" />
                        <span>Connected</span>
                      </div>
                      <div className="font-semibold text-green-600">{metrics.totalConnected.toLocaleString()}</div>
                    </div>
                    
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-sm text-gray-500 mb-1">
                        <Clock className="w-3 h-3" />
                        <span>Talk Time</span>
                      </div>
                      <div className="font-semibold text-amber-600">{formatTalkTime(metrics.totalTalkTime)}</div>
                    </div>
                    
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-sm text-gray-500 mb-1">
                        <Calendar className="w-3 h-3" />
                        <span>Scheduled</span>
                      </div>
                      <div className="font-semibold text-blue-600">{metrics.totalScheduled}</div>
                    </div>
                    
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="text-sm text-gray-500 mb-1">Successful</div>
                      <div className="font-semibold text-teal-600">{metrics.totalSuccessful}</div>
                    </div>
                  </div>

                  {/* FOP Metrics */}
                  <div className="grid grid-cols-2 gap-4 text-center border-l border-gray-200 pl-4">
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-sm text-gray-500 mb-1">
                        <Users className="w-3 h-3" />
                        <span>FOP Scheduled</span>
                      </div>
                      <div className="font-semibold text-indigo-600">{metrics.totalFopScheduled}</div>
                    </div>
                    
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="text-sm text-gray-500 mb-1">FOP Successful</div>
                      <div className="font-semibold text-pink-600">{metrics.totalFopSuccessful}</div>
                    </div>
                  </div>

                  {/* Success Rate Badge */}
                  <div className="text-center">
                    <div className="text-sm text-gray-500 mb-1">Success Rate</div>
                    <Badge className={`px-3 py-1 font-semibold ${getSuccessRateColor(metrics.averageSuccessRate)}`}>
                      {metrics.averageSuccessRate.toFixed(1)}%
                    </Badge>
                  </div>

                  {/* View Details Button */}
                  {onAgentSelect && (
                    <Button
                      onClick={() => onAgentSelect(agent.id)}
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                      size="sm"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  )}
                </div>
              </div>

              {/* Performance Indicators */}
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Performance indicators • Filter: {getFilterDescription()}</span>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${
                        metrics.averageSuccessRate >= 70 ? 'bg-green-500' : 
                        metrics.averageSuccessRate >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></div>
                      <span>Success Rate</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${
                        metrics.totalDials >= 100 ? 'bg-green-500' : 
                        metrics.totalDials >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></div>
                      <span>Activity</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${
                        (metrics.totalFopScheduled + metrics.totalFopSuccessful) > 0 ? 'bg-blue-500' : 'bg-gray-300'
                      }`}></div>
                      <span>FOP Activity</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default AgentTable;
