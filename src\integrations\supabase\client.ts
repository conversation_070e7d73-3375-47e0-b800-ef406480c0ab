// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://rgcpzhwcbcfoiepxjqki.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJnY3B6aHdjYmNmb2llcHhqcWtpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4NDE1NjYsImV4cCI6MjA2MzQxNzU2Nn0.Tb3CCWTulUNY1GWx18qcpD3LkYEVOdljDxuO6VsisQ8";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);