/* Enhanced Animations and Micro-interactions */

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Button hover effects with elevation */
.btn-enhanced {
  @apply transition-all duration-300 ease-in-out;
  transform: translateY(0);
}

.btn-enhanced:hover {
  @apply shadow-lg;
  transform: translateY(-2px);
}

.btn-enhanced:active {
  transform: translateY(0);
  @apply shadow-md;
}

/* Card hover effects */
.card-enhanced {
  @apply transition-all duration-300 ease-in-out;
  transform: translateY(0);
}

.card-enhanced:hover {
  @apply shadow-xl;
  transform: translateY(-4px);
}

/* Ripple effect for buttons */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}

/* Staggered animations for chart elements */
.chart-bar {
  animation: slideInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.chart-bar:nth-child(1) { animation-delay: 0ms; }
.chart-bar:nth-child(2) { animation-delay: 150ms; }
.chart-bar:nth-child(3) { animation-delay: 300ms; }
.chart-bar:nth-child(4) { animation-delay: 450ms; }
.chart-bar:nth-child(5) { animation-delay: 600ms; }

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade in animations */
.fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading skeleton animations */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Smooth focus states */
.focus-enhanced:focus {
  @apply ring-2 ring-blue-500 ring-opacity-50;
  outline: none;
  transition: box-shadow 0.2s ease-in-out;
}

/* Input field enhancements */
.input-enhanced {
  @apply transition-all duration-200 ease-in-out;
}

.input-enhanced:focus {
  @apply ring-2 ring-blue-500 ring-opacity-50 border-blue-500;
  transform: scale(1.02);
}

/* Dropdown animations */
.dropdown-enter {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.dropdown-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

.dropdown-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.dropdown-exit-active {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
  transition: opacity 200ms ease-in, transform 200ms ease-in;
}

/* Table row hover effects */
.table-row-enhanced {
  @apply transition-all duration-200 ease-in-out;
}

.table-row-enhanced:hover {
  @apply bg-gray-50 shadow-sm;
  transform: translateX(2px);
}

/* Form validation animations */
.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Success feedback animation */
.success-pulse {
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Gradient text animations */
.gradient-text {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Chart container animations */
.chart-container {
  @apply transition-all duration-500 ease-in-out;
}

.chart-container.loading {
  opacity: 0.7;
  transform: scale(0.98);
}

/* Modal animations */
.modal-overlay {
  animation: fadeInOverlay 0.3s ease-out;
}

@keyframes fadeInOverlay {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  animation: slideInModal 0.3s ease-out;
}

@keyframes slideInModal {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Progress bar animations */
.progress-bar {
  @apply transition-all duration-500 ease-out;
}

/* Notification animations */
.notification-enter {
  transform: translateX(100%);
  opacity: 0;
}

.notification-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: transform 300ms ease-out, opacity 300ms ease-out;
}

/* Responsive hover effects - disable on touch devices */
@media (hover: none) {
  .card-enhanced:hover,
  .btn-enhanced:hover,
  .table-row-enhanced:hover {
    transform: none;
    box-shadow: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .gradient-text {
    background: none;
    -webkit-text-fill-color: initial;
    color: inherit;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
