
import React, { useState } from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Trash2, Users, Download } from "lucide-react";
import ProjectForm from "./ProjectForm";
import AgentForm from "./AgentForm";
import AgentPanel from "./AgentPanel";
import InlineEdit from "@/components/ui/inline-edit";
import { addMissingProjects } from "@/utils/addMissingProjects";
import { toast } from "@/hooks/use-toast";

const ProjectManagementView = () => {
  const {
    projects,
    agents,
    selectedProject,
    setSelectedProject,
    isAgentPanelOpen,
    setIsAgentPanelOpen,
    isNewProjectModalOpen,
    setIsNewProjectModalOpen,
    isNewAgentModalOpen,
    setIsNewAgentModalOpen,
    updateProject,
    deleteProject,
    updateAgent,
    getAgentsByProjectId,
    getProjectById,
    isLoading
  } = useDashboard();

  const [isAddingMissingProjects, setIsAddingMissingProjects] = useState(false);

  const handleDeleteProject = async (projectId: string) => {
    if (confirm("Are you sure you want to delete this project? All associated agents will also be deleted.")) {
      const success = await deleteProject(projectId);
      if (success && selectedProject === projectId) {
        setSelectedProject("All");
      }
    }
  };

  const handleManageAgents = (projectId: string) => {
    setSelectedProject(projectId);
    setIsAgentPanelOpen(true);
  };

  const handleUpdateProjectName = async (projectId: string, newName: string) => {
    return await updateProject(projectId, { name: newName });
  };

  const handleUpdateAgentName = async (agentId: string, newName: string) => {
    return await updateAgent(agentId, { name: newName });
  };

  const handleAddMissingProjects = async () => {
    setIsAddingMissingProjects(true);
    try {
      const result = await addMissingProjects();
      if (result.success) {
        toast({
          title: "Success",
          description: result.message || "Missing projects have been added successfully!",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to add missing projects. Please try again.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error adding missing projects:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsAddingMissingProjects(false);
    }
  };

  const selectedProjectData = selectedProject !== "All" ? getProjectById(selectedProject) : null;
  
  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Project Management</h1>
        <Button onClick={() => setIsNewProjectModalOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Add Project
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-amplior-primary border-t-transparent rounded-full"></div>
        </div>
      ) : projects.length === 0 ? (
        <Card className="mb-6">
          <CardContent className="pt-6 text-center">
            <p className="text-gray-500 mb-4">No projects found. Create your first project to get started.</p>
            <Button onClick={() => setIsNewProjectModalOpen(true)}>
              <Plus className="mr-2 h-4 w-4" /> Create Project
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => {
            const projectAgents = getAgentsByProjectId(project.id);
            return (
              <Card key={project.id} className="overflow-hidden">
                <CardHeader className={`text-white ${project.color}`}>
                  <div className="flex justify-between">
                    <div className="flex-1 mr-4">
                      <InlineEdit
                        value={project.name}
                        onSave={(newName) => handleUpdateProjectName(project.id, newName)}
                        variant="title"
                        className="text-white"
                        placeholder="Enter project name"
                        maxLength={50}
                      />
                      <CardDescription className="text-white/80">Code: {project.code}</CardDescription>
                    </div>
                    <Button
                      variant="destructive"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => handleDeleteProject(project.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="mt-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium">Assigned Agents ({projectAgents.length})</h3>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleManageAgents(project.id)}
                      >
                        <Users className="h-4 w-4 mr-1" /> Manage
                      </Button>
                    </div>
                    {projectAgents.length === 0 ? (
                      <p className="text-sm text-gray-500">No agents assigned</p>
                    ) : (
                      <div className="space-y-2">
                        {projectAgents.map((agent) => (
                          <div key={agent.id} className="flex items-center p-2 bg-gray-50 rounded-md">
                            <InlineEdit
                              value={agent.name}
                              onSave={(newName) => handleUpdateAgentName(agent.id, newName)}
                              variant="default"
                              placeholder="Enter agent name"
                              maxLength={50}
                              className="flex-1"
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      <ProjectForm 
        isOpen={isNewProjectModalOpen} 
        setIsOpen={setIsNewProjectModalOpen} 
      />
      
      <AgentForm 
        isOpen={isNewAgentModalOpen} 
        setIsOpen={setIsNewAgentModalOpen} 
        projectId={selectedProject !== "All" ? selectedProject : undefined}
      />
      
      <AgentPanel 
        isOpen={isAgentPanelOpen} 
        setIsOpen={setIsAgentPanelOpen}
        project={selectedProjectData}
      />
    </div>
  );
};

export default ProjectManagementView;
