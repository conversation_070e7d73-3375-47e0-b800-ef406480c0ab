
export type Project = {
  id: string;
  name: string;
  code: string;
  color: string;
};

export type Agent = {
  id: string;
  name: string;
  projectId: string;
};

export type PerformanceMetric = {
  id: string;
  agentId: string;
  projectId: string;
  date: string;
  week?: string;
  dials: number;
  connected: number;
  talkTime: number;
  scheduledMeetings: number;
  successfulMeetings: number;
  fopScheduled?: number;
  fopSuccessful?: number;
  successRate: number;
};

export type TimeFrame = "Daily" | "Weekly" | "Monthly" | "Quarterly" | "Yearly";
export type WeekFrame = "Week 1" | "Week 2" | "Week 3" | "Week 4" | "Week 5";
export type FilteredProject = "All" | string;
export type PerformanceView = "projects" | "agents";

// Target Success Rate types
export type TargetSuccessRateData = {
  targetSuccessRate: number;
  colorCode: 'red' | 'yellow' | 'green' | 'dark-green';
  isOverAchieved: boolean;
  actualSuccessful: number;
  targetMeetings: number;
};

export type TargetConfig = {
  daily: number;
  weekly: number;
  monthly: number;
};

import { AgentPerformance } from "@/hooks/dashboard/useAgentPerformanceOperations";

export interface DashboardContextType {
  projects: Project[];
  agents: Agent[];
  metrics: PerformanceMetric[];
  selectedProject: FilteredProject;
  setSelectedProject: (project: FilteredProject) => void;
  timeFrame: TimeFrame;
  setTimeFrame: (timeFrame: TimeFrame) => void;
  selectedMonth: string;
  setSelectedMonth: (month: string) => void;
  selectedDate: Date;
  setSelectedDate: (date: Date) => void;
  selectedWeek: WeekFrame | null;
  setSelectedWeek: (week: WeekFrame | null) => void;
  getProjectById: (id: string) => Project | undefined;
  getProjectAgents: (projectId: string) => Agent[];
  getAgentById: (id: string) => Agent | undefined;
  getProjectMetrics: (projectId: string) => PerformanceMetric[];
  getFilteredMetrics: () => PerformanceMetric[];
  getTotalMetrics: () => {
    totalDials: number;
    totalConnected: number;
    totalTalkTime: number;
    scheduledMeetings: number;
    successfulMeetings: number;
  };
  getAvailableMonths: () => string[];
  getAvailableWeeks: (month: string) => WeekFrame[];
  currentView: 'dashboard' | 'performance' | 'data-entry' | 'project-management' | 'agent-backlog';
  setCurrentView: (view: 'dashboard' | 'performance' | 'data-entry' | 'project-management' | 'agent-backlog') => void;
  performanceView: PerformanceView;
  setPerformanceView: (view: PerformanceView) => void;
  isAgentPanelOpen: boolean;
  setIsAgentPanelOpen: (open: boolean) => void;
  isNewProjectModalOpen: boolean;
  setIsNewProjectModalOpen: (open: boolean) => void;
  isNewAgentModalOpen: boolean;
  setIsNewAgentModalOpen: (open: boolean) => void;
  isSidebarCollapsed: boolean;
  setIsSidebarCollapsed: (collapsed: boolean) => void;
  isLoading: boolean;
  addProject: (project: Omit<Project, 'id' | 'created_at' | 'updated_at'>) => Promise<boolean>;
  updateProject: (projectId: string, updates: Partial<Omit<Project, 'id' | 'created_at' | 'updated_at'>>) => Promise<boolean>;
  deleteProject: (projectId: string) => Promise<boolean>;
  addAgent: (agent: Omit<Agent, 'id' | 'created_at' | 'updated_at'>) => Promise<boolean>;
  updateAgent: (agentId: string, updates: Partial<Omit<Agent, 'id' | 'created_at' | 'updated_at'>>) => Promise<boolean>;
  deleteAgent: (agentId: string) => Promise<boolean>;
  getAgentsByProjectId: (projectId: string) => Agent[];
  getAgentInitials: (name: string) => string;
  getRandomColor: () => string;
  // New performance operations
  performanceData: AgentPerformance[];
  addPerformanceData: (data: Omit<AgentPerformance, 'id' | 'success_rate' | 'created_at' | 'updated_at'>) => Promise<boolean>;
  updatePerformanceData: (id: string, data: Partial<AgentPerformance>) => Promise<boolean>;
  deletePerformanceData: (id: string) => Promise<boolean>; // Add delete function
  getPerformanceByAgent: (agentId: string) => AgentPerformance[];
  getPerformanceByProject: (projectId: string) => AgentPerformance[];
  getAgentPerformanceMetrics: (agentId: string) => {
    totalDials: number;
    totalConnected: number;
    totalTalkTime: number;
    totalScheduled: number;
    totalSuccessful: number;
    totalFopScheduled: number;
    totalFopSuccessful: number;
    averageSuccessRate: number;
  };
  getProjectPerformanceMetrics: (projectId: string) => {
    totalDials: number;
    totalConnected: number;
    totalTalkTime: number;
    totalScheduled: number;
    totalSuccessful: number;
    totalFopScheduled: number;
    totalFopSuccessful: number;
    averageSuccessRate: number;
  };
  formatTalkTime: (seconds: number) => string;
  parseTalkTime: (timeString: string) => number;
  forceRefresh: () => void; // Add the missing forceRefresh function
}
