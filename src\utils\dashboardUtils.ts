
import { Project, Agent, PerformanceMetric, TimeFrame, FilteredProject, WeekFrame } from "@/types/dashboard";
import { format, isSameDay, isSameMonth, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns";

export const getProjectById = (projects: Project[], id: string): Project | undefined => {
  return projects.find(project => project.id === id);
};

export const getProjectAgents = (agents: Agent[], projectId: string): Agent[] => {
  if (projectId === "All" || projectId === "all") {
    return agents;
  }
  return agents.filter(agent => agent.projectId === projectId);
};

export const getAgentById = (agents: Agent[], id: string): Agent | undefined => {
  return agents.find(agent => agent.id === id);
};

export const getProjectMetrics = (metrics: PerformanceMetric[], projectId: string): PerformanceMetric[] => {
  if (projectId === "All" || projectId === "all") {
    return metrics;
  }
  return metrics.filter(metric => metric.projectId === projectId);
};

export const getFilteredMetrics = (
  metrics: PerformanceMetric[],
  selectedProject: FilteredProject,
  timeFrame: TimeFrame
): PerformanceMetric[] => {
  let filteredMetrics = metrics;
  
  // Filter by project
  if (selectedProject !== "All") {
    filteredMetrics = filteredMetrics.filter(metric => metric.projectId === selectedProject);
  }
  
  // Filter by date range based on selected timeframe
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  let startDate = new Date(today);
  
  switch (timeFrame) {
    case "Daily":
      // Already set to today
      break;
    case "Weekly":
      startDate.setDate(today.getDate() - 7);
      break;
    case "Monthly":
      startDate.setMonth(today.getMonth() - 1);
      break;
    case "Quarterly":
      startDate.setMonth(today.getMonth() - 3);
      break;
    case "Yearly":
      startDate.setFullYear(today.getFullYear() - 1);
      break;
  }
  
  filteredMetrics = filteredMetrics.filter(metric => {
    const metricDate = new Date(metric.date);
    return metricDate >= startDate && metricDate <= today;
  });
  
  return filteredMetrics;
};

export const getTotalMetrics = (filteredMetrics: PerformanceMetric[]) => {
  return {
    totalDials: filteredMetrics.reduce((sum, metric) => sum + metric.dials, 0),
    totalConnected: filteredMetrics.reduce((sum, metric) => sum + metric.connected, 0),
    totalTalkTime: filteredMetrics.reduce((sum, metric) => sum + metric.talkTime, 0),
    scheduledMeetings: filteredMetrics.reduce((sum, metric) => sum + metric.scheduledMeetings, 0),
    successfulMeetings: filteredMetrics.reduce((sum, metric) => sum + metric.successfulMeetings, 0),
  };
};

// Get available months that have data
export const getAvailableMonths = (metrics: PerformanceMetric[]): string[] => {
  const months = new Set<string>();
  metrics.forEach(metric => {
    const date = new Date(metric.date);
    const monthName = format(date, 'MMM');
    months.add(monthName);
  });
  return Array.from(months).sort((a, b) => {
    const monthOrder = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return monthOrder.indexOf(a) - monthOrder.indexOf(b);
  });
};

// Get available weeks for a specific month that have data
export const getAvailableWeeks = (metrics: PerformanceMetric[], selectedMonth: string): WeekFrame[] => {
  const weeks = new Set<WeekFrame>();
  metrics.forEach(metric => {
    const date = new Date(metric.date);
    const monthName = format(date, 'MMM');
    if (monthName === selectedMonth && metric.week) {
      weeks.add(metric.week as WeekFrame);
    }
  });
  return Array.from(weeks).sort((a, b) => {
    const weekOrder = ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5'];
    return weekOrder.indexOf(a) - weekOrder.indexOf(b);
  });
};

// Enhanced filtering function with date picker and week support
export const getEnhancedFilteredMetrics = (
  metrics: PerformanceMetric[],
  selectedProject: FilteredProject,
  timeFrame: TimeFrame,
  selectedDate?: Date,
  selectedMonth?: string,
  selectedWeek?: WeekFrame | null
): PerformanceMetric[] => {
  let filteredMetrics = metrics;

  // Filter by project
  if (selectedProject !== "All") {
    filteredMetrics = filteredMetrics.filter(metric => metric.projectId === selectedProject);
  }

  // Filter by time frame
  switch (timeFrame) {
    case "Daily":
      if (selectedDate) {
        filteredMetrics = filteredMetrics.filter(metric => {
          const metricDate = new Date(metric.date);
          return isSameDay(metricDate, selectedDate);
        });
      }
      break;
    case "Weekly":
      if (selectedWeek && selectedMonth) {
        filteredMetrics = filteredMetrics.filter(metric => {
          const metricDate = new Date(metric.date);
          const monthName = format(metricDate, 'MMM');
          return monthName === selectedMonth && metric.week === selectedWeek;
        });
      }
      break;
    case "Monthly":
      if (selectedMonth) {
        filteredMetrics = filteredMetrics.filter(metric => {
          const metricDate = new Date(metric.date);
          const monthName = format(metricDate, 'MMM');
          return monthName === selectedMonth;
        });
      }
      break;
    default:
      // For other time frames, use the original logic
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      let startDate = new Date(today);

      switch (timeFrame) {
        case "Quarterly":
          startDate.setMonth(today.getMonth() - 3);
          break;
        case "Yearly":
          startDate.setFullYear(today.getFullYear() - 1);
          break;
      }

      filteredMetrics = filteredMetrics.filter(metric => {
        const metricDate = new Date(metric.date);
        return metricDate >= startDate && metricDate <= today;
      });
  }

  return filteredMetrics;
};
