import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

export interface AgentPerformance {
  id: string;
  agent_id: string;
  project_id: string;
  date: string;
  week?: string;
  dials: number;
  connected: number;
  talk_time: number;
  scheduled_meetings: number;
  successful_meetings: number;
  fop_scheduled: number;
  fop_successful: number;
  fop_projects?: string[];
  success_rate: number;
  created_at: string;
  updated_at: string;
}

// Type for inserting new performance data
export interface AgentPerformanceInsert {
  agent_id: string;
  project_id: string;
  date: string;
  week?: string;
  dials: number;
  connected: number;
  talk_time: number;
  scheduled_meetings: number;
  successful_meetings: number;
  fop_scheduled: number;
  fop_successful: number;
  fop_projects?: string[];
}

export const useAgentPerformanceOperations = () => {
  const [performanceData, setPerformanceData] = useState<AgentPerformance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Enhanced connection pool management
  const createConnectionPool = () => {
    return supabase;
  };

  // Robust fetch with connection pooling and retry logic
  const fetchPerformanceData = async (retry = false, forceRefresh = false) => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('📊 Fetching performance data...', { retry, forceRefresh });
      
      // Create a new connection for this request
      const client = createConnectionPool();
      
      // Use timeout and connection pooling for better reliability
      const { data, error: fetchError } = await Promise.race([
        (client as any)
          .from('agent_performance')
          .select('*')
          .order('created_at', { ascending: false }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 15000)
        )
      ]) as any;

      if (fetchError) {
        // If table doesn't exist, start with empty data
        if (fetchError.code === '42P01') {
          console.log('Table does not exist, starting with empty data');
          setPerformanceData([]);
          toast({
            title: "Ready for Data Entry",
            description: "No performance data found. You can start entering real data through the Daily Logs section.",
            variant: "default"
          });
        } else {
          throw fetchError;
        }
      } else if (data) {
        console.log('✅ Performance data fetched successfully:', data.length, 'records');
        setPerformanceData(data as AgentPerformance[]);
      }
    } catch (error: any) {
      console.error('❌ Error fetching performance data:', error);
      setError('Failed to load performance data');
      
      // Start with empty data instead of sample data
      setPerformanceData([]);
      
      if (!retry && retryCount < 3) {
        // Progressive retry delays: 2s, 4s, 8s
        const delay = Math.pow(2, retryCount + 1) * 1000;
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          fetchPerformanceData(true);
        }, delay);
        
        toast({
          title: "Retrying Connection...",
          description: `Attempting to reconnect in ${delay / 1000} seconds...`,
          variant: "default"
        });
      } else {
        toast({
          title: "Connection Issue",
          description: "Unable to connect to database. Please check your internet connection and try again.",
          variant: "destructive"
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Force refresh function for manual refresh
  const forceRefresh = () => {
    console.log('🔄 Force refreshing performance data...');
    setRetryCount(0);
    fetchPerformanceData(false, true);
  };

  // Fetch performance data from Supabase
  useEffect(() => {
    fetchPerformanceData();

    // Enhanced real-time subscription with reconnection logic
    let reconnectTimeout: NodeJS.Timeout;
    let subscription: any;

    const setupSubscription = () => {
      console.log('📡 Setting up real-time subscription...');
      
      subscription = supabase
        .channel('agent-performance-changes')
        .on('postgres_changes', { 
          event: '*', 
          schema: 'public', 
          table: 'agent_performance' 
        }, (payload) => {
          console.log('🔄 Real-time performance data change detected:', {
            eventType: payload.eventType,
            timestamp: new Date().toISOString()
          });
          
          try {
            if (payload.eventType === 'INSERT') {
              console.log('➕ Adding new performance record to state');
              setPerformanceData((current) => {
                const newRecord = payload.new as AgentPerformance;
                // Ensure we don't add duplicates
                const exists = current.some(item => item.id === newRecord.id);
                if (exists) {
                  console.log('⚠️ Record already exists, skipping duplicate insert');
                  return current;
                }
                return [newRecord, ...current];
              });
              
              toast({
                title: "✅ Data Synced",
                description: "New performance data has been added successfully.",
              });
            } else if (payload.eventType === 'UPDATE') {
              console.log('✏️ Updating performance record in state');
              setPerformanceData((current) => 
                current.map(item => item.id === payload.new.id ? payload.new as AgentPerformance : item)
              );
            } else if (payload.eventType === 'DELETE') {
              console.log('🗑️ [REAL-TIME] DELETE event received');
              
              const deletedId = (payload.old as any)?.id || (payload.new as any)?.id;
              
              if (!deletedId) {
                console.error('🗑️ [REAL-TIME] Error: No ID found in DELETE payload');
                return;
              }

              console.log(`🗑️ [REAL-TIME] Removing item with ID: ${deletedId} from state`);
              
              setPerformanceData((current) => {
                const newData = current.filter(item => item.id !== deletedId);
                console.log(`🗑️ [REAL-TIME] State updated! Items after deletion: ${newData.length}`);
                return newData;
              });
            }
          } catch (err) {
            console.error('❌ Error processing real-time update:', err);
          }
        })
        .subscribe((status) => {
          console.log('📡 Real-time subscription status:', status);
          
          if (status === 'CHANNEL_ERROR' || status === 'CLOSED') {
            console.log('🔄 Real-time connection lost, attempting to reconnect...');
            // Attempt to reconnect after 5 seconds
            reconnectTimeout = setTimeout(() => {
              console.log('🔄 Reconnecting real-time subscription...');
              setupSubscription();
            }, 5000);
          } else if (status === 'SUBSCRIBED') {
            console.log('✅ Real-time subscription established successfully');
            // Clear any pending reconnection attempts
            if (reconnectTimeout) {
              clearTimeout(reconnectTimeout);
            }
          }
        });
    };

    setupSubscription();

    return () => {
      console.log('🔌 Cleaning up real-time subscription');
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }
      if (subscription) {
        supabase.removeChannel(subscription);
      }
    };
  }, []);

  // Convert seconds to hh:mm:ss format
  const formatTalkTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Convert hh:mm:ss format to seconds
  const parseTalkTime = (timeString: string): number => {
    const parts = timeString.split(':');
    if (parts.length !== 3) return 0;
    
    const hours = parseInt(parts[0], 10) || 0;
    const minutes = parseInt(parts[1], 10) || 0;
    const seconds = parseInt(parts[2], 10) || 0;
    
    return (hours * 3600) + (minutes * 60) + seconds;
  };

  // Enhanced add performance data with connection pooling and retry
  const addPerformanceData = async (data: AgentPerformanceInsert): Promise<boolean> => {
    try {
      console.log('💾 Adding performance data to Supabase:', data);
      
      // Calculate success rate
      const success_rate = data.scheduled_meetings > 0 
        ? (data.successful_meetings / data.scheduled_meetings) * 100 
        : 0;

      const dataWithSuccessRate = {
        ...data,
        success_rate
      };

      // Create a dedicated connection for this operation
      const client = createConnectionPool();
      
      // Add timeout and retry logic
      const { data: insertedData, error } = await Promise.race([
        (client as any)
          .from('agent_performance')
          .insert([dataWithSuccessRate])
          .select()
          .single(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Insert operation timeout')), 10000)
        )
      ]) as any;

      if (error) {
        console.error('❌ Supabase insert error:', error);
        
        // Handle specific database errors
        if (error.code === '23505') {
          throw new Error('Data for this agent and date already exists. Please check your entries.');
        } else if (error.message?.includes('connection')) {
          throw new Error('Database connection failed. Please check your internet and try again.');
        } else {
          throw error;
        }
      }
      
      console.log('✅ Performance data added successfully:', insertedData);
      
      // Force refresh to ensure we have the latest data
      setTimeout(() => {
        console.log('🔄 Auto-refreshing data after insert...');
        forceRefresh();
      }, 500);
      
      return true;
    } catch (error: any) {
      console.error('❌ Error adding performance data:', error);
      
      // Provide user-friendly error messages
      let userMessage = 'Failed to save performance data.';
      
      if (error.message?.includes('timeout')) {
        userMessage = 'Request timed out. The server might be busy. Please try again.';
      } else if (error.message?.includes('connection')) {
        userMessage = 'Connection issue detected. Please check your internet and try again.';
      } else if (error.message?.includes('already exists')) {
        userMessage = 'Data for this agent and date already exists.';
      }
      
      toast({
        title: "❌ Upload Failed",
        description: userMessage,
        variant: "destructive"
      });
      
      return false;
    }
  };

  // Enhanced update with better error handling
  const updatePerformanceData = async (id: string, data: Partial<AgentPerformance>) => {
    try {
      console.log('✏️ Updating performance data:', { id, data });
      
      // Calculate success rate if needed
      const updateData = { ...data };
      if (updateData.successful_meetings !== undefined && updateData.scheduled_meetings !== undefined) {
        updateData.success_rate = updateData.scheduled_meetings > 0 
          ? (updateData.successful_meetings / updateData.scheduled_meetings) * 100 
          : 0;
      }

      const client = createConnectionPool();
      
      const { error } = await Promise.race([
        (client as any)
          .from('agent_performance')
          .update(updateData)
          .eq('id', id),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Update operation timeout')), 10000)
        )
      ]) as any;

      if (error) {
        console.error('❌ Supabase update error:', error);
        throw error;
      }
      
      console.log('✅ Performance data updated successfully');
      return true;
    } catch (error: any) {
      console.error('❌ Error updating performance data:', error);
      throw new Error(error.message || 'Failed to update performance data');
    }
  };

  // Enhanced delete function with better error handling
  const deletePerformanceData = async (id: string) => {
    try {
      console.log('🗑️ [DELETE API] Starting delete operation for ID:', id);
      
      // Immediately remove from UI state for instant feedback
      setPerformanceData((current) => {
        console.log('🗑️ [DELETE API] Immediately removing from UI state');
        return current.filter(item => item.id !== id);
      });
      
      const client = createConnectionPool();
      
      // Call Supabase delete API with timeout
      const { error } = await Promise.race([
        (client as any)
          .from('agent_performance')
          .delete()
          .eq('id', id),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Delete operation timeout')), 10000)
        )
      ]) as any;

      if (error) {
        console.error('❌ [DELETE API] Supabase delete error:', error);
        // If delete failed, restore the item back to the UI
        console.log('🔄 [DELETE API] Restoring item back to UI due to delete failure');
        fetchPerformanceData(); // Refresh to get current state
        throw error;
      }
      
      console.log('✅ [DELETE API] Supabase delete successful for ID:', id);
      return true;
    } catch (error: any) {
      console.error('❌ [DELETE API] Error deleting performance data:', error);
      throw new Error(error.message || 'Failed to delete performance data');
    }
  };

  // Get performance data by agent
  const getPerformanceByAgent = (agentId: string): AgentPerformance[] => {
    return performanceData.filter(item => item.agent_id === agentId);
  };

  const getPerformanceByProject = (projectId: string): AgentPerformance[] => {
    return performanceData.filter(item => item.project_id === projectId);
  };

  const getAgentMetrics = (agentId: string) => {
    const agentData = getPerformanceByAgent(agentId);
    
    return {
      totalDials: agentData.reduce((sum, item) => sum + item.dials, 0),
      totalConnected: agentData.reduce((sum, item) => sum + item.connected, 0),
      totalTalkTime: agentData.reduce((sum, item) => sum + item.talk_time, 0),
      totalScheduled: agentData.reduce((sum, item) => sum + item.scheduled_meetings, 0),
      totalSuccessful: agentData.reduce((sum, item) => sum + item.successful_meetings, 0),
      totalFopScheduled: agentData.reduce((sum, item) => sum + (item.fop_scheduled || 0), 0),
      totalFopSuccessful: agentData.reduce((sum, item) => sum + (item.fop_successful || 0), 0),
      averageSuccessRate: agentData.length > 0 ? 
        agentData.reduce((sum, item) => sum + item.success_rate, 0) / agentData.length : 0
    };
  };

  const getProjectMetrics = (projectId: string) => {
    const projectData = getPerformanceByProject(projectId);
    
    return {
      totalDials: projectData.reduce((sum, item) => sum + item.dials, 0),
      totalConnected: projectData.reduce((sum, item) => sum + item.connected, 0),
      totalTalkTime: projectData.reduce((sum, item) => sum + item.talk_time, 0),
      totalScheduled: projectData.reduce((sum, item) => sum + item.scheduled_meetings, 0),
      totalSuccessful: projectData.reduce((sum, item) => sum + item.successful_meetings, 0),
      totalFopScheduled: projectData.reduce((sum, item) => sum + (item.fop_scheduled || 0), 0),
      totalFopSuccessful: projectData.reduce((sum, item) => sum + (item.fop_successful || 0), 0),
      averageSuccessRate: projectData.length > 0 ? 
        projectData.reduce((sum, item) => sum + item.success_rate, 0) / projectData.length : 0
    };
  };

  const retryFetch = () => {
    setRetryCount(0);
    fetchPerformanceData();
  };

  return {
    performanceData,
    isLoading,
    error,
    addPerformanceData,
    updatePerformanceData,
    deletePerformanceData,
    getPerformanceByAgent,
    getPerformanceByProject,
    getAgentMetrics,
    getProjectMetrics,
    formatTalkTime,
    parseTalkTime,
    retryFetch,
    forceRefresh,
  };
};
