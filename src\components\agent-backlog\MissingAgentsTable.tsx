
import React from "react";
import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, UserX } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface MissingAgent {
  agent_id: string;
  agent_name: string;
  project_name: string;
  project_color: string;
}

interface MissingAgentsTableProps {
  missingAgents: MissingAgent[];
  getAgentInitials: (name: string) => string;
  timeframeDisplay: string;
  isLoading: boolean;
}

const MissingAgentsTable: React.FC<MissingAgentsTableProps> = ({
  missingAgents,
  getAgentInitials,
  timeframeDisplay,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto mb-3"></div>
        <p className="text-gray-500">Loading missing agents data...</p>
      </div>
    );
  }

  if (missingAgents.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-green-400 mb-3">
          <CheckCircle className="h-12 w-12 mx-auto" />
        </div>
        <p className="text-green-600 font-medium">All agents have filled their data!</p>
        <p className="text-sm text-gray-400 mt-1">
          Great work! Everyone is up to date for {timeframeDisplay}.
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow className="border-b border-gray-100">
            <TableHead className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Agent
            </TableHead>
            <TableHead className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Project
            </TableHead>
            <TableHead className="text-center py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Period
            </TableHead>
            <TableHead className="text-center py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {missingAgents.map((agent) => (
            <TableRow 
              key={agent.agent_id} 
              className="border-b border-gray-50 hover:bg-orange-25 transition-colors"
            >
              <TableCell className="py-4 px-6">
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-gradient-to-br from-orange-500 to-red-600 text-white font-medium text-sm">
                      {getAgentInitials(agent.agent_name)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="font-medium text-gray-900 text-sm">
                    {agent.agent_name}
                  </span>
                </div>
              </TableCell>
              <TableCell className="py-4 px-6">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${agent.project_color}`}></div>
                  <span className="text-sm text-gray-700">{agent.project_name}</span>
                </div>
              </TableCell>
              <TableCell className="py-4 px-6 text-center">
                <span className="text-sm text-gray-600">{timeframeDisplay}</span>
              </TableCell>
              <TableCell className="py-4 px-6 text-center">
                <Badge className="bg-orange-50 text-orange-700 border-orange-200 text-xs font-medium">
                  <UserX className="h-3 w-3 mr-1" />
                  Not Filled
                </Badge>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default MissingAgentsTable;
