import React, { useState, useEffect } from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import UniversalDropdown, { DropdownOption } from "@/components/ui/universal-dropdown";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Clock, Phone, PhoneCall, Calendar, CheckCircle, TrendingUp, TrendingDown, Minus, RefreshCw, Users, Activity, ArrowUp, ArrowDown, UserX } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { formatTalkTime } from "@/lib/formatters";
import { cn } from "@/lib/utils";
import { getPerformanceStatus, getPerformanceColor, getPerformanceLabel } from "@/utils/performanceThresholds";
import { isWithinInterval, startOfDay, endOfDay, startOfWeek, endOfWeek, format, addWeeks, startOfMonth } from "date-fns";
import MissingAgentsTable from "./MissingAgentsTable";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface RealTimeAgentMetrics {
  agent_id: string;
  agent_name: string;
  project_name: string;
  project_color: string;
  total_dials: number;
  total_connected: number;
  total_talk_time: number;
  total_scheduled: number;
  total_successful: number;
  success_rate: number;
}

interface MissingAgent {
  agent_id: string;
  agent_name: string;
  project_name: string;
  project_color: string;
}

type SortField = "agent_name" | "project_name" | "total_dials" | "total_connected" | "total_talk_time" | "total_scheduled" | "total_successful" | "performance";
type SortDirection = "asc" | "desc";
type ViewMode = "performance" | "missing";

const AgentBacklogView = () => {
  const {
    projects,
    agents,
    selectedProject,
    setSelectedProject,
    getAgentInitials,
    getProjectById,
    selectedDate,
    timeFrame,
    selectedWeek,
    selectedMonth,
    performanceData,
    getAgentById
  } = useDashboard();

  const { toast } = useToast();
  const [performanceFilter, setPerformanceFilter] = useState<"all" | "good" | "average" | "needs-attention">("all");
  const [realTimeMetrics, setRealTimeMetrics] = useState<RealTimeAgentMetrics[]>([]);
  const [missingAgents, setMissingAgents] = useState<MissingAgent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [isRealTimeUpdating, setIsRealTimeUpdating] = useState(false);
  const [sortField, setSortField] = useState<SortField>("agent_name");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [viewMode, setViewMode] = useState<ViewMode>("performance");

  // 🎯 UNIFIED DATE FILTERING LOGIC - EXACT SAME AS PERFORMANCE HISTORY
  const getWeekDateRange = (weekFrame: string) => {
    const weekNumber = parseInt(weekFrame.replace('Week ', ''));

    if (selectedMonth && selectedMonth !== "All Time") {
      const currentYear = new Date().getFullYear();
      const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
      const monthIndex = monthNames.indexOf(selectedMonth);

      if (monthIndex !== -1) {
        const monthStart = new Date(currentYear, monthIndex, 1);
        const weekStart = addWeeks(startOfWeek(monthStart), weekNumber - 1);
        const weekEnd = endOfWeek(weekStart);
        return { weekStart, weekEnd };
      }
    }

    const monthStart = startOfMonth(new Date());
    const weekStart = addWeeks(startOfWeek(monthStart), weekNumber - 1);
    const weekEnd = endOfWeek(weekStart);
    return { weekStart, weekEnd };
  };

  // 🚀 OPTIMIZED FILTERING LOGIC FOR REAL-TIME SYNC + NAME NORMALIZATION
  const processPerformanceDataWithUnifiedLogic = () => {
    const startTime = performance.now();
    console.log('⚡ [AGENT-BACKLOG] Processing performance data with optimized real-time logic:', {
      totalRecords: performanceData.length,
      timeFrame,
      selectedDate: selectedDate?.toISOString().split('T')[0],
      selectedWeek,
      selectedMonth,
      selectedProject,
      timestamp: new Date().toISOString()
    });

    // 🔍 DEBUG: Show sample of raw data
    if (performanceData.length > 0) {
      console.log('📋 [AGENT-BACKLOG] Sample of raw performance data:', {
        firstRecord: performanceData[0],
        totalRecords: performanceData.length,
        uniqueDates: [...new Set(performanceData.map(item => item.date))].sort(),
        uniqueAgents: [...new Set(performanceData.map(item => item.agent_id))].length,
        dateRange: {
          earliest: Math.min(...performanceData.map(item => new Date(item.date).getTime())),
          latest: Math.max(...performanceData.map(item => new Date(item.date).getTime()))
        }
      });

      // 🔍 SPECIFIC DEBUG: Check for Aditi Tiwari's data
      const aditiRecords = performanceData.filter(item => {
        const agent = getAgentById(item.agent_id);
        return agent?.name?.toLowerCase().includes('aditi') || agent?.name?.toLowerCase().includes('tiwari');
      });

      if (aditiRecords.length > 0) {
        console.log('👤 [AGENT-BACKLOG] Aditi Tiwari records found:', {
          totalRecords: aditiRecords.length,
          records: aditiRecords.map(record => ({
            id: record.id,
            date: record.date,
            agentId: record.agent_id,
            agentName: getAgentById(record.agent_id)?.name,
            projectId: record.project_id,
            projectName: getProjectById(record.project_id)?.name,
            dials: record.dials,
            connected: record.connected,
            talkTime: record.talk_time,
            scheduled: record.scheduled_meetings,
            successful: record.successful_meetings
          }))
        });
      }
    }

    // 🆕 HELPER FUNCTION: Normalize agent names by taking first two words
    const normalizeName = (fullName: string): string => {
      return fullName.split(" ").slice(0, 2).join(" ").toLowerCase().trim();
    };

    // STEP 1: Apply EXACT same filtering logic as Performance History
    const filteredData = performanceData.filter(item => {
      const agent = getAgentById(item.agent_id);
      const isAditiTiwari = agent?.name?.toLowerCase().includes('aditi') || agent?.name?.toLowerCase().includes('tiwari');

      if (isAditiTiwari) {
        console.log('👤 [AGENT-BACKLOG] Filtering Aditi Tiwari item:', {
          itemId: item.id,
          itemDate: item.date,
          itemWeek: item.week,
          agentId: item.agent_id,
          agentName: agent?.name,
          projectId: item.project_id,
          dials: item.dials,
          currentFilters: {
            timeFrame,
            selectedDate: selectedDate?.toISOString().split('T')[0],
            selectedWeek,
            selectedMonth,
            selectedProject
          }
        });
      }

      const itemDate = new Date(item.date);

      // Apply project filter first
      if (selectedProject !== "All" && item.project_id !== selectedProject) {
        console.log('❌ [AGENT-BACKLOG] Filtered out by project:', item.project_id, 'expected:', selectedProject);
        return false;
      }

      // Apply IDENTICAL date filters as Performance History
      switch (timeFrame) {
        case "Daily":
          if (selectedDate) {
            const matchesDate = isWithinInterval(itemDate, {
              start: startOfDay(selectedDate),
              end: endOfDay(selectedDate)
            });
            
            const exactDateMatch = item.date === selectedDate.toISOString().split('T')[0];
            
            if (isAditiTiwari) {
              console.log('👤📅 [AGENT-BACKLOG] Aditi Tiwari Daily filter check:', {
                itemDate: item.date,
                selectedDate: selectedDate.toISOString().split('T')[0],
                intervalMatch: matchesDate,
                exactMatch: exactDateMatch,
                finalResult: matchesDate || exactDateMatch,
                dials: item.dials
              });
            }

            return matchesDate || exactDateMatch;
          }
          
          const today = new Date().toISOString().split('T')[0];
          const matchesToday = item.date === today || isWithinInterval(itemDate, {
            start: startOfDay(new Date()),
            end: endOfDay(new Date())
          });
          
          console.log('📅 [AGENT-BACKLOG] Daily filter (today):', {
            itemDate: item.date,
            today,
            matches: matchesToday
          });
          
          return matchesToday;

        case "Weekly":
          if (selectedWeek) {
            const directWeekMatch = item.week === selectedWeek;
            
            const { weekStart, weekEnd } = getWeekDateRange(selectedWeek);
            const dateRangeMatch = isWithinInterval(itemDate, {
              start: startOfDay(weekStart),
              end: endOfDay(weekEnd)
            });
            
            console.log('📅 [AGENT-BACKLOG] Weekly filter check:', {
              itemWeek: item.week,
              selectedWeek,
              directMatch: directWeekMatch,
              weekRange: { start: weekStart.toISOString().split('T')[0], end: weekEnd.toISOString().split('T')[0] },
              dateRangeMatch,
              finalResult: directWeekMatch || dateRangeMatch
            });
            
            return directWeekMatch || dateRangeMatch;
          }
          
          const currentWeekMatch = isWithinInterval(itemDate, {
            start: startOfWeek(new Date()),
            end: endOfWeek(new Date())
          });
          
          console.log('📅 [AGENT-BACKLOG] Weekly filter (current week):', {
            itemDate: item.date,
            matches: currentWeekMatch
          });
          
          return currentWeekMatch;

        case "Monthly":
          if (selectedMonth && selectedMonth !== "All Time") {
            const itemMonthName = format(itemDate, 'MMM');
            const monthMatch = itemMonthName === selectedMonth;
            
            console.log('📅 [AGENT-BACKLOG] Monthly filter check:', {
              itemMonth: itemMonthName,
              selectedMonth,
              matches: monthMatch
            });
            
            return monthMatch;
          }
          
          console.log('📅 [AGENT-BACKLOG] Monthly filter (all time): showing all data');
          return true;

        default:
          console.log('📅 [AGENT-BACKLOG] Default filter: showing all data');
          return true;
      }
    }).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    console.log('📊 [AGENT-BACKLOG] Filtered data result:', {
      totalItems: performanceData.length,
      filteredItems: filteredData.length,
      filters: { timeFrame, selectedDate: selectedDate?.toISOString().split('T')[0], selectedWeek, selectedMonth, selectedProject }
    });

    // 🔍 SPECIFIC DEBUG: Check filtered Aditi Tiwari data
    const filteredAditiRecords = filteredData.filter(item => {
      const agent = getAgentById(item.agent_id);
      return agent?.name?.toLowerCase().includes('aditi') || agent?.name?.toLowerCase().includes('tiwari');
    });

    if (filteredAditiRecords.length > 0) {
      console.log('👤 [AGENT-BACKLOG] Filtered Aditi Tiwari records:', {
        totalFilteredRecords: filteredAditiRecords.length,
        records: filteredAditiRecords.map(record => ({
          id: record.id,
          date: record.date,
          agentName: getAgentById(record.agent_id)?.name,
          projectName: getProjectById(record.project_id)?.name,
          dials: record.dials,
          connected: record.connected,
          talkTime: record.talk_time,
          scheduled: record.scheduled_meetings,
          successful: record.successful_meetings
        }))
      });
    }

    // STEP 2: DEDUPLICATE and aggregate data by agent
    console.log('🔍 [AGENT-BACKLOG] Starting aggregation with filtered data:', {
      filteredRecords: filteredData.length,
      selectedDate: selectedDate?.toISOString().split('T')[0],
      timeFrame,
      selectedProject
    });

    // 🔧 DEDUPLICATION: Remove duplicate records for the same agent-date combination
    // Keep only the most recent record (highest created_at timestamp) for each agent-date pair
    const deduplicatedData = new Map<string, AgentPerformance>();

    filteredData.forEach(record => {
      const key = `${record.agent_id}-${record.date}`;
      const existing = deduplicatedData.get(key);

      if (!existing || new Date(record.created_at || record.updated_at || '').getTime() >
                       new Date(existing.created_at || existing.updated_at || '').getTime()) {
        deduplicatedData.set(key, record);
      }
    });

    const finalData = Array.from(deduplicatedData.values());

    console.log('🔧 [AGENT-BACKLOG] Deduplication complete:', {
      originalRecords: filteredData.length,
      deduplicatedRecords: finalData.length,
      removedDuplicates: filteredData.length - finalData.length
    });

    // 🔍 SPECIFIC DEBUG: Check final Aditi Tiwari data after deduplication
    const finalAditiRecords = finalData.filter(item => {
      const agent = getAgentById(item.agent_id);
      return agent?.name?.toLowerCase().includes('aditi') || agent?.name?.toLowerCase().includes('tiwari');
    });

    if (finalAditiRecords.length > 0) {
      console.log('👤 [AGENT-BACKLOG] Final Aditi Tiwari records after deduplication:', {
        totalRecords: finalAditiRecords.length,
        records: finalAditiRecords.map(record => ({
          id: record.id,
          date: record.date,
          agentName: getAgentById(record.agent_id)?.name,
          projectName: getProjectById(record.project_id)?.name,
          dials: record.dials,
          connected: record.connected,
          talkTime: record.talk_time,
          scheduled: record.scheduled_meetings,
          successful: record.successful_meetings,
          createdAt: record.created_at,
          updatedAt: record.updated_at
        }))
      });
    }

    // STEP 3: Aggregate deduplicated data by agent
    const agentMap = new Map<string, RealTimeAgentMetrics>();

    finalData.forEach((record, index) => {
      const agentId = record.agent_id;
      const existing = agentMap.get(agentId);

      const agent = getAgentById(agentId);
      const project = getProjectById(record.project_id);

      console.log(`📊 [AGENT-BACKLOG] Processing record ${index + 1}/${finalData.length}:`, {
        recordId: record.id,
        agentId: record.agent_id,
        agentName: agent?.name,
        projectId: record.project_id,
        projectName: project?.name,
        date: record.date,
        dials: record.dials,
        connected: record.connected,
        talkTime: record.talk_time,
        scheduled: record.scheduled_meetings,
        successful: record.successful_meetings,
        isExistingAgent: !!existing
      });

      if (existing) {
        console.log(`🔄 [AGENT-BACKLOG] Updating existing agent ${agent?.name}:`, {
          before: {
            dials: existing.total_dials,
            connected: existing.total_connected,
            talkTime: existing.total_talk_time,
            scheduled: existing.total_scheduled,
            successful: existing.total_successful
          },
          adding: {
            dials: record.dials || 0,
            connected: record.connected || 0,
            talkTime: record.talk_time || 0,
            scheduled: record.scheduled_meetings || 0,
            successful: record.successful_meetings || 0
          }
        });

        existing.total_dials += record.dials || 0;
        existing.total_connected += record.connected || 0;
        existing.total_talk_time += record.talk_time || 0;
        existing.total_scheduled += record.scheduled_meetings || 0;
        existing.total_successful += record.successful_meetings || 0;
        existing.success_rate = existing.total_scheduled > 0
          ? (existing.total_successful / existing.total_scheduled) * 100
          : 0;

        console.log(`✅ [AGENT-BACKLOG] Updated agent ${agent?.name}:`, {
          after: {
            dials: existing.total_dials,
            connected: existing.total_connected,
            talkTime: existing.total_talk_time,
            scheduled: existing.total_scheduled,
            successful: existing.total_successful,
            successRate: existing.success_rate
          }
        });
      } else {
        const newMetrics = {
          agent_id: agentId,
          agent_name: agent?.name || 'Unknown Agent',
          project_name: project?.name || 'Unknown Project',
          project_color: project?.color || 'bg-gray-500',
          total_dials: record.dials || 0,
          total_connected: record.connected || 0,
          total_talk_time: record.talk_time || 0,
          total_scheduled: record.scheduled_meetings || 0,
          total_successful: record.successful_meetings || 0,
          success_rate: record.success_rate || 0,
        };

        console.log(`➕ [AGENT-BACKLOG] Adding new agent ${agent?.name}:`, newMetrics);
        agentMap.set(agentId, newMetrics);
      }
    });

    // STEP 3: 🚀 FIXED MISSING AGENTS LOGIC WITH NAME NORMALIZATION
    const allAgents = selectedProject === "All"
      ? agents
      : agents.filter(agent => agent.projectId === selectedProject);

    // 🔥 NEW LOGIC: Create a Set of normalized agent names who have submitted data
    const filledAgentsNormalizedSet = new Set<string>();
    
    filteredData.forEach(record => {
      const agent = getAgentById(record.agent_id);
      if (agent) {
        const normalizedName = normalizeName(agent.name);
        filledAgentsNormalizedSet.add(normalizedName);
        console.log('🔄 [AGENT-BACKLOG] Added normalized name to filled set:', {
          originalName: agent.name,
          normalizedName: normalizedName
        });
      }
    });

    console.log('🔍 [AGENT-BACKLOG] Filled agents normalized set:', {
      filledNormalizedNames: Array.from(filledAgentsNormalizedSet),
      totalAgents: allAgents.length,
      filteredDataCount: filteredData.length
    });

    // 🎯 UPDATED LOGIC: Filter out agents whose normalized names match any filled agent
    const missingAgentsList = allAgents.filter(agent => {
      const normalizedAgentName = normalizeName(agent.name);
      const isNotFilled = !filledAgentsNormalizedSet.has(normalizedAgentName);
      
      console.log('🔍 [AGENT-BACKLOG] Checking agent for missing status:', {
        agentName: agent.name,
        normalizedName: normalizedAgentName,
        isNotFilled: isNotFilled,
        filledSet: Array.from(filledAgentsNormalizedSet)
      });
      
      return isNotFilled;
    }).map(agent => {
      const project = getProjectById(agent.projectId);
      return {
        agent_id: agent.id,
        agent_name: agent.name,
        project_name: project?.name || 'Unknown Project',
        project_color: project?.color || 'bg-gray-500',
      };
    });

    const metricsArray = Array.from(agentMap.values());
    console.log('✅ [AGENT-BACKLOG] Final aggregated metrics (FIXED WITH NAME NORMALIZATION):', {
      agentsWithData: metricsArray.length,
      missingAgents: missingAgentsList.length,
      totalAgents: allAgents.length,
      fixApplied: 'Agent-centric missing logic with name normalization (first 2 words)'
    });

    // 🚀 OPTIMIZED STATE UPDATES: Batch updates for better performance
    // 🔍 FINAL DEBUG: Show Aditi Tiwari's final aggregated data
    const finalAditiMetrics = metricsArray.find(agent =>
      agent.agent_name?.toLowerCase().includes('aditi') || agent.agent_name?.toLowerCase().includes('tiwari')
    );

    if (finalAditiMetrics) {
      console.log('👤 [AGENT-BACKLOG] Final Aditi Tiwari aggregated metrics:', {
        agentName: finalAditiMetrics.agent_name,
        projectName: finalAditiMetrics.project_name,
        totalDials: finalAditiMetrics.total_dials,
        totalConnected: finalAditiMetrics.total_connected,
        totalTalkTime: finalAditiMetrics.total_talk_time,
        totalScheduled: finalAditiMetrics.total_scheduled,
        totalSuccessful: finalAditiMetrics.total_successful,
        successRate: finalAditiMetrics.success_rate
      });
    }

    setRealTimeMetrics(metricsArray);
    setMissingAgents(missingAgentsList);
    setLastUpdated(new Date());

    // 📊 PERFORMANCE MONITORING: Track processing time
    const endTime = performance.now();
    const processingTime = endTime - startTime;
    console.log('✅ [AGENT-BACKLOG] Data processing completed:', {
      processingTimeMs: Math.round(processingTime),
      agentsProcessed: metricsArray.length,
      missingAgentsFound: missingAgentsList.length,
      performanceRating: processingTime < 100 ? 'Excellent' : processingTime < 500 ? 'Good' : 'Needs Optimization'
    });
  };

  // 🔄 REAL-TIME SYNC: Process data when filters or performance data changes
  useEffect(() => {
    console.log('🔄 [AGENT-BACKLOG] Filter change detected, reprocessing data...');
    processPerformanceDataWithUnifiedLogic();
  }, [timeFrame, selectedDate, selectedWeek, selectedMonth, selectedProject, performanceData]);

  // 🚀 ENHANCED REAL-TIME SUBSCRIPTION: Direct subscription with immediate state updates
  useEffect(() => {
    console.log('📡 [AGENT-BACKLOG] Setting up dedicated real-time subscription...');

    const subscription = supabase
      .channel('agent-backlog-real-time')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'agent_performance'
      }, (payload) => {
        console.log('🔄 [AGENT-BACKLOG] Real-time update received:', {
          eventType: payload.eventType,
          timestamp: new Date().toISOString(),
          affectedRecord: payload.new || payload.old
        });

        // 🚀 IMMEDIATE STATE UPDATE: Process the specific change instantly
        if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
          const newRecord = payload.new as any;
          console.log('⚡ [AGENT-BACKLOG] Processing immediate state update for:', {
            agentId: newRecord?.agent_id,
            projectId: newRecord?.project_id,
            date: newRecord?.date
          });

          // Check if this record affects current filters
          const affectsCurrentView =
            selectedProject === "All" ||
            selectedProject === newRecord?.project_id;

          if (affectsCurrentView) {
            console.log('✅ [AGENT-BACKLOG] Record affects current view, triggering immediate update');

            // Show real-time update indicator
            setIsRealTimeUpdating(true);

            // Trigger immediate reprocessing with minimal delay
            setTimeout(() => {
              processPerformanceDataWithUnifiedLogic();

              // Hide update indicator after processing
              setTimeout(() => setIsRealTimeUpdating(false), 1000);

              toast({
                title: "📊 Agent Backlog Updated",
                description: "Agent backlog data has been synced with latest changes.",
                duration: 2000
              });
            }, 50); // Minimal delay for data consistency
          } else {
            console.log('ℹ️ [AGENT-BACKLOG] Record does not affect current view, skipping update');
          }
        } else if (payload.eventType === 'DELETE') {
          console.log('🗑️ [AGENT-BACKLOG] Processing DELETE event');
          // Show real-time update indicator
          setIsRealTimeUpdating(true);

          // Always reprocess on delete as it might affect missing agents list
          setTimeout(() => {
            processPerformanceDataWithUnifiedLogic();

            // Hide update indicator after processing
            setTimeout(() => setIsRealTimeUpdating(false), 1000);

            toast({
              title: "📊 Agent Backlog Updated",
              description: "Agent backlog data has been updated after record deletion.",
              duration: 2000
            });
          }, 50);
        }
      })
      .subscribe((status) => {
        console.log('📡 [AGENT-BACKLOG] Real-time subscription status:', status);
        if (status === 'SUBSCRIBED') {
          console.log('✅ [AGENT-BACKLOG] Real-time subscription active and ready');
        }
      });

    return () => {
      console.log('🔌 [AGENT-BACKLOG] Cleaning up real-time subscription...');
      supabase.removeChannel(subscription);
    };
  }, [selectedProject]); // Include selectedProject to re-subscribe when project filter changes

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Calculate performance status using thresholds
  const getAgentPerformanceStatus = (metrics: RealTimeAgentMetrics) => {
    const { total_dials, total_connected, total_talk_time } = metrics;

    const dialsStatus = getPerformanceStatus('dials', total_dials, timeFrame);
    const connectedStatus = getPerformanceStatus('connected', total_connected, timeFrame);
    const talkTimeStatus = getPerformanceStatus('talkTime', total_talk_time, timeFrame);

    const statusPriority = { low: 0, average: 1, good: 2 };
    const statuses = [dialsStatus, connectedStatus, talkTimeStatus];
    const worstStatus = statuses.reduce((worst, current) =>
      statusPriority[current] < statusPriority[worst] ? current : worst, 'good');

    const statusColors = {
      good: "bg-green-100 text-green-800",
      average: "bg-yellow-100 text-yellow-800",
      low: "bg-red-100 text-red-800"
    };

    const statusLabels = {
      good: "Good Performance",
      average: "Average Performance",
      low: "Needs Attention"
    };

    const mappedStatus = worstStatus === 'low' ? 'needs-attention' : worstStatus;

    return {
      status: mappedStatus,
      label: statusLabels[worstStatus],
      color: statusColors[worstStatus]
    };
  };

  // Filter and sort agents
  const getFilteredAndSortedAgents = () => {
    let filtered = realTimeMetrics.filter(metrics => {
      if (performanceFilter === "all") return true;
      const { status } = getAgentPerformanceStatus(metrics);
      return status === performanceFilter;
    });

    filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortField) {
        case "agent_name":
          aValue = a.agent_name;
          bValue = b.agent_name;
          break;
        case "project_name":
          aValue = a.project_name;
          bValue = b.project_name;
          break;
        case "performance":
          const aStatus = getAgentPerformanceStatus(a).status;
          const bStatus = getAgentPerformanceStatus(b).status;
          const statusOrder = { "good": 3, "average": 2, "needs-attention": 1 };
          aValue = statusOrder[aStatus as keyof typeof statusOrder] || 0;
          bValue = statusOrder[bStatus as keyof typeof statusOrder] || 0;
          break;
        default:
          aValue = a[sortField];
          bValue = b[sortField];
      }

      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc" 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      return sortDirection === "asc" 
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number);
    });

    return filtered;
  };

  const agentsToShow = getFilteredAndSortedAgents();

  // Calculate summary stats
  const totalAgents = realTimeMetrics.length;
  const goodPerformers = realTimeMetrics.filter(metrics => {
    return getAgentPerformanceStatus(metrics).status === "good";
  }).length;

  const needsAttention = realTimeMetrics.filter(metrics => {
    return getAgentPerformanceStatus(metrics).status === "needs-attention";
  }).length;

  // Get current timeframe display
  const getTimeframeDisplay = () => {
    switch (timeFrame) {
      case "Daily":
        return selectedDate?.toLocaleDateString() || new Date().toLocaleDateString();
      case "Weekly":
        return selectedWeek || "Current Week";
      case "Monthly":
        return selectedMonth || "Current Month";
      default:
        return "Current Period";
    }
  };

  // Column header component
  const SortableHeader = ({ field, label }: { field: SortField, label: string }) => (
    <TableHead 
      className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:text-gray-700"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center gap-1">
        {label}
        {sortField === field && (
          sortDirection === "asc" ? 
            <ArrowUp className="h-3 w-3" /> : 
            <ArrowDown className="h-3 w-3" />
        )}
      </div>
    </TableHead>
  );

  // Create project dropdown options
  const projectOptions: DropdownOption[] = [
    { value: "All", label: "All Projects" },
    ...projects.map((project) => ({
      value: project.id,
      label: project.name,
    }))
  ];

  // Create performance dropdown options
  const performanceOptions: DropdownOption[] = [
    { value: "all", label: "All Agents" },
    { value: "good", label: "Good Performance" },
    { value: "average", label: "Average Performance" },
    { value: "needs-attention", label: "Needs Attention" }
  ];

  // Handle project selection
  const handleProjectSelect = (option: DropdownOption) => {
    setSelectedProject(option.value);
  };

  // Handle performance filter selection
  const handlePerformanceSelect = (option: DropdownOption) => {
    setPerformanceFilter(option.value as "all" | "good" | "average" | "needs-attention");
  };

  // Find selected options
  const selectedProjectOption = projectOptions.find(option => option.value === selectedProject) || null;
  const selectedPerformanceOption = performanceOptions.find(option => option.value === performanceFilter) || null;

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* Header Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Agent Backlog Tracker</h1>
            <p className="text-sm text-gray-600 mt-1">
              Monitor all agents' performance and backlog status in real-time for {getTimeframeDisplay()}
              <span className="ml-2 text-green-600 font-medium">
                • Synced with Performance History • Fixed Multi-project Logic • Name Normalization
              </span>
              {isRealTimeUpdating && (
                <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 animate-pulse">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-1 animate-ping"></div>
                  Syncing...
                </span>
              )}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={processPerformanceDataWithUnifiedLogic}
              disabled={isLoading}
              className="h-9"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <div className="text-xs text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-semibold text-gray-900">{totalAgents}</p>
                  <p className="text-sm text-gray-600">Total Agents</p>
                </div>
                <div className="p-2 bg-blue-50 rounded-lg">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-semibold text-green-600">{goodPerformers}</p>
                  <p className="text-sm text-gray-600">Good Performance</p>
                </div>
                <div className="p-2 bg-green-50 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-semibold text-red-600">{needsAttention}</p>
                  <p className="text-sm text-gray-600">Needs Attention</p>
                </div>
                <div className="p-2 bg-red-50 rounded-lg">
                  <TrendingDown className="h-5 w-5 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-semibold text-orange-600">{missingAgents.length}</p>
                  <p className="text-sm text-gray-600">Not Filled</p>
                </div>
                <div className="p-2 bg-orange-50 rounded-lg">
                  <UserX className="h-5 w-5 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === "performance" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("performance")}
          >
            Performance View
          </Button>
          <Button
            variant={viewMode === "missing" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("missing")}
          >
            Not Filled by Agent ({missingAgents.length})
          </Button>
        </div>

        {/* Filters - Only show for performance view */}
        {viewMode === "performance" && (
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Project</label>
              <UniversalDropdown
                options={projectOptions}
                placeholder="All Projects"
                value={selectedProjectOption}
                onChange={handleProjectSelect}
                className="w-full"
              />
            </div>

            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Performance</label>
              <UniversalDropdown
                options={performanceOptions}
                placeholder="All Agents"
                value={selectedPerformanceOption}
                onChange={handlePerformanceSelect}
                className="w-full"
              />
            </div>
          </div>
        )}
      </div>

      {/* Main Content */}
      {viewMode === "performance" ? (
        // Performance View
        <Card className="bg-white shadow-sm border-0">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-medium">
              Agent Performance Overview - {timeFrame}
            </CardTitle>
            <CardDescription className="text-sm">
              Showing {agentsToShow.length} of {totalAgents} agents for {getTimeframeDisplay()}
              {isLoading && " (Loading...)"}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {isLoading ? (
              <div className="text-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400 mb-3" />
                <p className="text-gray-500">Loading agent performance data...</p>
              </div>
            ) : agentsToShow.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-3">
                  <Users className="h-12 w-12 mx-auto" />
                </div>
                <p className="text-gray-500 font-medium">No agents found</p>
                <p className="text-sm text-gray-400 mt-1">Try adjusting your filters or check if data exists for this period.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-b border-gray-100">
                      <SortableHeader field="agent_name" label="Agent" />
                      <SortableHeader field="project_name" label="Project" />
                      <SortableHeader field="performance" label="Performance" />
                      <SortableHeader field="total_dials" label="Dials" />
                      <SortableHeader field="total_connected" label="Connected" />
                      <SortableHeader field="total_talk_time" label="Talk Time" />
                      <SortableHeader field="total_scheduled" label="Scheduled" />
                      <SortableHeader field="total_successful" label="Successful" />
                      <TableHead className="text-center py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wider">Backlog</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {agentsToShow.map((metrics) => {
                      const { status, label, color } = getAgentPerformanceStatus(metrics);
                      const successfulTarget = timeFrame === "Daily" ? 1 : timeFrame === "Weekly" ? 5 : 20;
                      const backlog = Math.max(0, successfulTarget - metrics.total_successful);

                      const dialsStatus = getPerformanceStatus('dials', metrics.total_dials, timeFrame);
                      const connectedStatus = getPerformanceStatus('connected', metrics.total_connected, timeFrame);
                      const talkTimeStatus = getPerformanceStatus('talkTime', metrics.total_talk_time, timeFrame);

                      return (
                        <TableRow key={metrics.agent_id} className="border-b border-gray-50 hover:bg-gray-25 transition-colors">
                          <TableCell className="py-4 px-6">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-8 w-8">
                                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium text-sm">
                                  {getAgentInitials(metrics.agent_name)}
                                </AvatarFallback>
                              </Avatar>
                              <span className="font-medium text-gray-900 text-sm">{metrics.agent_name}</span>
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6">
                            <div className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${metrics.project_color}`}></div>
                              <span className="text-sm text-gray-700">{metrics.project_name}</span>
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6 text-center">
                            <Badge 
                              variant="secondary" 
                              className={cn(
                                "text-xs font-medium",
                                status === "good" && "bg-green-50 text-green-700 border-green-200",
                                status === "average" && "bg-yellow-50 text-yellow-700 border-yellow-200",
                                status === "needs-attention" && "bg-red-50 text-red-700 border-red-200"
                              )}
                            >
                              {status === "good" && "Good"}
                              {status === "average" && "Average"}
                              {status === "needs-attention" && "Needs Attention"}
                            </Badge>
                          </TableCell>
                          <TableCell className="py-4 px-6 text-center">
                            <div className="flex flex-col items-center">
                              <span className="font-semibold text-gray-900 text-sm">{metrics.total_dials}</span>
                              <span className={cn(
                                "text-xs font-medium",
                                getPerformanceColor(dialsStatus)
                              )}>
                                {getPerformanceLabel(dialsStatus)}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6 text-center">
                            <div className="flex flex-col items-center">
                              <span className="font-semibold text-gray-900 text-sm">{metrics.total_connected}</span>
                              <span className={cn(
                                "text-xs font-medium",
                                getPerformanceColor(connectedStatus)
                              )}>
                                {getPerformanceLabel(connectedStatus)}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6 text-center">
                            <div className="flex flex-col items-center">
                              <span className="font-semibold text-gray-900 text-sm">{formatTalkTime(metrics.total_talk_time)}</span>
                              <span className={cn(
                                "text-xs font-medium",
                                getPerformanceColor(talkTimeStatus)
                              )}>
                                {getPerformanceLabel(talkTimeStatus)}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6 text-center">
                            <div className="flex items-center justify-center gap-1">
                              {metrics.total_scheduled > 0 && (
                                <CheckCircle className="h-3 w-3 text-green-600" />
                              )}
                              <span className={cn(
                                "font-semibold text-sm",
                                metrics.total_scheduled > 0 ? "text-gray-900" : "text-red-600"
                              )}>{metrics.total_scheduled}</span>
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6 text-center">
                            <div className="flex items-center justify-center gap-1">
                              {metrics.total_successful > 0 && (
                                <CheckCircle className="h-3 w-3 text-green-600" />
                              )}
                              <span className={cn(
                                "font-semibold text-sm",
                                metrics.total_successful > 0 ? "text-gray-900" : "text-red-600"
                              )}>{metrics.total_successful}</span>
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6 text-center">
                            <Badge
                              className={cn(
                                "text-xs font-medium",
                                backlog === 0
                                  ? "bg-green-50 text-green-700 border-green-200"
                                  : "bg-red-50 text-red-700 border-red-200"
                              )}
                            >
                              {backlog === 0 ? "On Track" : `${backlog} Behind`}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        // Missing Agents View
        <MissingAgentsTable
          missingAgents={missingAgents}
          getAgentInitials={getAgentInitials}
          timeframeDisplay={getTimeframeDisplay()}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default AgentBacklogView;
