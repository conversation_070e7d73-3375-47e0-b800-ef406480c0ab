
-- Fix the upsert_agent_performance function security warning
CREATE OR REPLACE FUNCTION public.upsert_agent_performance(
  p_agent_id uuid, 
  p_project_id uuid, 
  p_date date, 
  p_dials integer DEFAULT 0, 
  p_connected integer DEFAULT 0, 
  p_talk_time integer DEFAULT 0, 
  p_scheduled_meetings integer DEFAULT 0, 
  p_successful_meetings integer DEFAULT 0, 
  p_fop_scheduled integer DEFAULT 0, 
  p_fop_successful integer DEFAULT 0
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public, pg_catalog
AS $$
DECLARE
  v_record_id UUID;
  v_success_rate NUMERIC;
BEGIN
  -- Calculate success rate
  v_success_rate := CASE 
    WHEN p_scheduled_meetings > 0 THEN (p_successful_meetings::NUMERIC / p_scheduled_meetings) * 100 
    ELSE 0 
  END;

  -- Insert or update the record
  INSERT INTO public.agent_performance (
    agent_id, project_id, date, dials, connected, talk_time, 
    scheduled_meetings, successful_meetings, fop_scheduled, fop_successful,
    success_rate, last_sync_at, sync_source
  ) VALUES (
    p_agent_id, p_project_id, p_date, p_dials, p_connected, p_talk_time,
    p_scheduled_meetings, p_successful_meetings, p_fop_scheduled, p_fop_successful,
    v_success_rate, now(), 'runo_webhook'
  )
  ON CONFLICT (agent_id, date) DO UPDATE SET
    dials = agent_performance.dials + EXCLUDED.dials,
    connected = agent_performance.connected + EXCLUDED.connected,
    talk_time = agent_performance.talk_time + EXCLUDED.talk_time,
    scheduled_meetings = agent_performance.scheduled_meetings + EXCLUDED.scheduled_meetings,
    successful_meetings = agent_performance.successful_meetings + EXCLUDED.successful_meetings,
    fop_scheduled = agent_performance.fop_scheduled + EXCLUDED.fop_scheduled,
    fop_successful = agent_performance.fop_successful + EXCLUDED.fop_successful,
    success_rate = CASE 
      WHEN (agent_performance.scheduled_meetings + EXCLUDED.scheduled_meetings) > 0 
      THEN ((agent_performance.successful_meetings + EXCLUDED.successful_meetings)::NUMERIC / 
            (agent_performance.scheduled_meetings + EXCLUDED.scheduled_meetings)) * 100 
      ELSE 0 
    END,
    last_sync_at = now(),
    sync_source = 'runo_webhook',
    updated_at = now()
  RETURNING id INTO v_record_id;

  RETURN v_record_id;
END;
$$;
