import { supabase } from '../src/integrations/supabase/client';

// Projects to add to the database
const projectsToAdd = [
  {
    name: "Rare",
    code: "RA",
    color: "bg-green-500"
  },
  {
    name: "SIS 2.0",
    code: "S2",
    color: "bg-purple-500"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    code: "UF",
    color: "bg-red-500"
  },
  {
    name: "UniQ",
    code: "UQ",
    color: "bg-rose-500"
  }
];

async function addMissingProjects() {
  console.log('Starting to add missing projects...');
  
  try {
    // First, check which projects already exist
    const { data: existingProjects, error: fetchError } = await supabase
      .from('projects')
      .select('name')
      .in('name', projectsToAdd.map(p => p.name));
    
    if (fetchError) {
      console.error('Error fetching existing projects:', fetchError);
      return;
    }
    
    const existingProjectNames = existingProjects?.map(p => p.name) || [];
    console.log('Existing projects:', existingProjectNames);
    
    // Filter out projects that already exist
    const projectsToInsert = projectsToAdd.filter(
      project => !existingProjectNames.includes(project.name)
    );
    
    if (projectsToInsert.length === 0) {
      console.log('All projects already exist in the database.');
      return;
    }
    
    console.log('Projects to add:', projectsToInsert.map(p => p.name));
    
    // Insert the missing projects
    const { data, error } = await supabase
      .from('projects')
      .insert(projectsToInsert)
      .select();
    
    if (error) {
      console.error('Error adding projects:', error);
      return;
    }
    
    console.log('Successfully added projects:', data);
    console.log('✅ All missing projects have been added to the database!');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the script
addMissingProjects();
