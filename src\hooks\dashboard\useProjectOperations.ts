
import { useState, useEffect } from "react";
import { Project } from "@/types/dashboard";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

export const useProjectOperations = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(true);

  // Fetch projects from Supabase
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setIsLoadingProjects(true);
        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) throw error;
        if (data) setProjects(data);
      } catch (error) {
        console.error('Error fetching projects:', error);
        toast({
          title: "Error",
          description: "Failed to load projects. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoadingProjects(false);
      }
    };

    fetchProjects();

    // Subscribe to realtime changes for projects
    const projectsSubscription = supabase
      .channel('projects-changes')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'projects' 
      }, (payload) => {
        if (payload.eventType === 'INSERT') {
          setProjects((current) => [payload.new as Project, ...current]);
        } else if (payload.eventType === 'UPDATE') {
          setProjects((current) => 
            current.map(project => project.id === payload.new.id ? payload.new as Project : project)
          );
        } else if (payload.eventType === 'DELETE') {
          setProjects((current) => 
            current.filter(project => project.id !== payload.old.id)
          );
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(projectsSubscription);
    };
  }, []);

  // Add a new project to Supabase
  const addProject = async (project: Omit<Project, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { error } = await supabase
        .from('projects')
        .insert([project]);

      if (error) throw error;
      
      toast({
        title: "Success",
        description: "Project added successfully",
      });
      return true;
    } catch (error) {
      console.error('Error adding project:', error);
      toast({
        title: "Error",
        description: "Failed to add project. Please try again.",
        variant: "destructive"
      });
      return false;
    }
  };

  // Update a project in Supabase
  const updateProject = async (projectId: string, updates: Partial<Omit<Project, 'id' | 'created_at' | 'updated_at'>>) => {
    try {
      const { error } = await supabase
        .from('projects')
        .update(updates)
        .eq('id', projectId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Project updated successfully",
      });
      return true;
    } catch (error) {
      console.error('Error updating project:', error);
      toast({
        title: "Error",
        description: "Failed to update project. Please try again.",
        variant: "destructive"
      });
      return false;
    }
  };

  // Delete a project from Supabase
  const deleteProject = async (projectId: string) => {
    try {
      // First delete all agents associated with this project
      const { error: agentsError } = await supabase
        .from('agents')
        .delete()
        .eq('project_id', projectId);

      if (agentsError) throw agentsError;

      // Then delete the project
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Project deleted successfully",
      });
      return true;
    } catch (error) {
      console.error('Error deleting project:', error);
      toast({
        title: "Error",
        description: "Failed to delete project. Please try again.",
        variant: "destructive"
      });
      return false;
    }
  };

  return {
    projects,
    isLoadingProjects,
    addProject,
    updateProject,
    deleteProject,
  };
};
