
/**
 * Formats minutes into a human-readable time format (e.g., "2h 30m")
 */
export const formatTime = (minutes: number): string => {
  if (minutes === 0) return "0m";
  
  const hours = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);
  
  if (hours === 0) return `${mins}m`;
  if (mins === 0) return `${hours}h`;
  
  return `${hours}h ${mins}m`;
};

/**
 * Formats seconds into a human-readable time format (e.g., "2h 30m", "45m", "30s")
 */
export const formatTalkTime = (seconds: number): string => {
  if (seconds === 0) return "0s";
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    if (minutes === 0) return `${hours}h`;
    return `${hours}h ${minutes}m`;
  }
  
  if (minutes > 0) {
    if (secs === 0) return `${minutes}m`;
    return `${minutes}m ${secs}s`;
  }
  
  return `${secs}s`;
};
