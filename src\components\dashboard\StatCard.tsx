
import React from "react";
import { cn } from "@/lib/utils";

interface StatCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  className?: string;
  color?: string;
  change?: {
    value: number;
    isPositive: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  className,
  color = "bg-purple-100",
  change,
}) => {
  return (
    <div className={cn(
      "bg-white rounded-xl p-6 shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer group",
      className
    )}>
      <div className="flex items-center justify-between mb-4">
        <div className="text-gray-600 text-sm font-medium">{title}</div>
        {icon && (
          <div className="p-2 rounded-lg bg-gray-50 group-hover:scale-110 transition-transform duration-200">
            {icon}
          </div>
        )}
      </div>

      <div className="space-y-2">
        <div className="text-3xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors duration-200">
          {value}
        </div>

        {change && (
          <div className="flex items-center gap-1">
            <div
              className={`text-sm font-medium flex items-center gap-1 ${
                change.isPositive ? "text-green-600" : "text-red-600"
              }`}
            >
              <span className={`text-xs ${change.isPositive ? "text-green-500" : "text-red-500"}`}>
                {change.isPositive ? "↗" : "↘"}
              </span>
              {change.value}%
            </div>
            <span className="text-xs text-gray-500">vs last period</span>
          </div>
        )}
      </div>

      {/* Enhanced progress bar with gradient */}
      <div className="mt-4">
        <div className={`h-1.5 w-full rounded-full bg-gray-100 overflow-hidden`}>
          <div
            className={`h-full rounded-full bg-gradient-to-r ${
              color.includes('purple') ? 'from-purple-400 to-purple-600' :
              color.includes('green') ? 'from-green-400 to-green-600' :
              color.includes('amber') ? 'from-amber-400 to-amber-600' :
              'from-blue-400 to-blue-600'
            } transition-all duration-500 group-hover:w-full`}
            style={{ width: '70%' }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default StatCard;
