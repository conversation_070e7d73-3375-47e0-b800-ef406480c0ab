
import { useState } from "react";
import { FilteredProject } from "@/types/dashboard";
import { useProjectOperations } from "./dashboard/useProjectOperations";
import { useAgentOperations } from "./dashboard/useAgentOperations";
import { useMetricsOperations } from "./dashboard/useMetricsOperations";
import { useUIState } from "./dashboard/useUIState";

export const useDashboardState = () => {
  const [selectedProject, setSelectedProject] = useState<FilteredProject>("All");
  
  const { projects, isLoadingProjects, addProject, updateProject, deleteProject } = useProjectOperations();
  const { agents, isLoadingAgents, addAgent, updateAgent, deleteAgent, getAgentsByProjectId } = useAgentOperations();
  const { metrics } = useMetricsOperations();
  const {
    timeFrame, setTimeFrame,
    selectedMonth, setSelectedMonth,
    selectedDate, setSelectedDate,
    selectedWeek, setSelectedWeek,
    currentView, setCurrentView,
    performanceView, setPerformanceView,
    isAgentPanelOpen, setIsAgentPanelOpen,
    isNewProjectModalOpen, setIsNewProjectModalOpen,
    isNewAgentModalOpen, setIsNewAgentModalOpen,
    isSidebarCollapsed, setIsSidebarCollapsed,
  } = useUIState(selectedProject);

  // Utility functions
  const getAgentInitials = (name: string): string => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Generate a random color for a project or agent with good contrast
  const getRandomColor = (): string => {
    const colors = [
      "bg-red-500", "bg-blue-500", "bg-green-500", "bg-purple-500",
      "bg-pink-500", "bg-indigo-500", "bg-teal-500", "bg-orange-500",
      "bg-emerald-500", "bg-cyan-500", "bg-violet-500", "bg-fuchsia-500",
      "bg-rose-500", "bg-amber-600", "bg-lime-600", "bg-sky-500"
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };
  
  // Determine if anything is still loading
  const isLoading = isLoadingProjects || isLoadingAgents;

  return {
    projects,
    agents,
    metrics,
    selectedProject,
    setSelectedProject,
    timeFrame,
    setTimeFrame,
    selectedMonth,
    setSelectedMonth,
    selectedDate,
    setSelectedDate,
    selectedWeek,
    setSelectedWeek,
    currentView,
    setCurrentView,
    performanceView,
    setPerformanceView,
    isAgentPanelOpen,
    setIsAgentPanelOpen,
    isNewProjectModalOpen,
    setIsNewProjectModalOpen,
    isNewAgentModalOpen,
    setIsNewAgentModalOpen,
    isSidebarCollapsed,
    setIsSidebarCollapsed,
    isLoading,
    addProject,
    updateProject,
    deleteProject,
    addAgent,
    updateAgent,
    deleteAgent,
    getAgentsByProjectId,
    getAgentInitials,
    getRandomColor,
  };
};
