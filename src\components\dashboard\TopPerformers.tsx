
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, TrendingUp, Trophy, Medal, Award, CheckCircle, CalendarDays, Phone, Loader2 } from "lucide-react";
import { useDashboard } from "@/contexts/DashboardContext";

interface PerformerData {
  agent_id: string;
  agent_name: string;
  project_name: string;
  project_code: string;
  project_color: string;
  total_successful: number;
  total_scheduled: number;
  total_dials: number;
}

const TopPerformers = () => {
  const { timeFrame, getAgentInitials } = useDashboard();

  // Fetch real-time performer data from Supabase
  const { data: performers, isLoading, error } = useQuery({
    queryKey: ['top-performers', timeFrame],
    queryFn: async (): Promise<PerformerData[]> => {
      const today = new Date();
      let dateFilter = '';

      if (timeFrame === "Daily") {
        dateFilter = today.toISOString().split('T')[0];
      } else if (timeFrame === "Weekly") {
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        dateFilter = `date >= '${weekStart.toISOString().split('T')[0]}' AND date <= '${weekEnd.toISOString().split('T')[0]}'`;
      } else if (timeFrame === "Monthly") {
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        dateFilter = `date >= '${monthStart.toISOString().split('T')[0]}' AND date <= '${monthEnd.toISOString().split('T')[0]}'`;
      }

      let query = supabase
        .from('agent_performance')
        .select(`
          agent_id,
          dials,
          scheduled_meetings,
          successful_meetings,
          agents!inner(name),
          projects!inner(name, code, color)
        `);

      if (timeFrame === "Daily") {
        query = query.eq('date', dateFilter);
      } else {
        query = query.filter('date', 'gte', dateFilter.split(' >= \'')[1].split('\'')[0]);
        if (timeFrame === "Weekly" || timeFrame === "Monthly") {
          query = query.filter('date', 'lte', dateFilter.split(' <= \'')[1].split('\'')[0]);
        }
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching performers:', error);
        throw error;
      }

      // Group by agent and sum the metrics
      const agentMap = new Map();
      
      data?.forEach((record: any) => {
        const agentId = record.agent_id;
        if (!agentMap.has(agentId)) {
          agentMap.set(agentId, {
            agent_id: agentId,
            agent_name: record.agents.name,
            project_name: record.projects.name,
            project_code: record.projects.code,
            project_color: record.projects.color,
            total_successful: 0,
            total_scheduled: 0,
            total_dials: 0,
          });
        }
        
        const agent = agentMap.get(agentId);
        agent.total_successful += record.successful_meetings || 0;
        agent.total_scheduled += record.scheduled_meetings || 0;
        agent.total_dials += record.dials || 0;
      });

      // Convert to array and sort by successful meetings (descending)
      const sortedPerformers = Array.from(agentMap.values())
        .filter(agent => agent.total_successful > 0 || agent.total_scheduled > 0 || agent.total_dials > 0)
        .sort((a, b) => {
          if (a.total_successful !== b.total_successful) {
            return b.total_successful - a.total_successful;
          }
          if (a.total_scheduled !== b.total_scheduled) {
            return b.total_scheduled - a.total_scheduled;
          }
          return b.total_dials - a.total_dials;
        })
        .slice(0, 5);

      return sortedPerformers;
    },
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
  });

  const getRankIcon = (index: number) => {
    switch (index) {
      case 0:
        return <Trophy className="w-5 h-5 text-yellow-500" />;
      case 1:
        return <Medal className="w-5 h-5 text-gray-400" />;
      case 2:
        return <Award className="w-5 h-5 text-amber-600" />;
      default:
        return <span className="w-5 h-5 flex items-center justify-center text-sm font-bold text-gray-500">#{index + 1}</span>;
    }
  };

  const getRankBadge = (index: number) => {
    switch (index) {
      case 0:
        return <Badge className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white text-xs">⭐ Star Performer</Badge>;
      case 1:
        return <Badge className="bg-gradient-to-r from-gray-400 to-gray-600 text-white text-xs">🥈 Runner Up</Badge>;
      case 2:
        return <Badge className="bg-gradient-to-r from-amber-500 to-amber-700 text-white text-xs">🥉 Top 3</Badge>;
      default:
        return <Badge variant="outline" className="text-xs">Top 5</Badge>;
    }
  };

  if (isLoading) {
    return (
      <Card className="transition-all duration-200 hover:shadow-lg">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            <CardTitle className="text-xl">Top 5 Performers</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3 text-gray-500">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>Loading performance data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="transition-all duration-200 hover:shadow-lg">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            <CardTitle className="text-xl">Top 5 Performers</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="text-red-500 mb-2">⚠️ Error Loading Data</div>
          <p className="text-gray-600 text-sm">Failed to fetch performance data</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-all duration-200 hover:shadow-lg">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Star className="h-5 w-5 text-yellow-500" />
          <CardTitle className="text-xl">Top 5 Performers</CardTitle>
        </div>
        <p className="text-sm text-gray-500 italic pt-1">
          Ranked by Successful Meetings – {timeFrame} View
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {performers && performers.length > 0 ? (
          performers.map((performer, index) => {
            const initials = getAgentInitials(performer.agent_name);
            
            return (
              <div
                key={performer.agent_id}
                className={`p-4 rounded-lg border transition-all duration-200 hover:scale-[1.02] hover:shadow-md cursor-pointer ${
                  index === 0
                    ? 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-200 shadow-sm'
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                }`}
                onClick={() => {
                  alert(`${performer.agent_name} Performance Details:\n\nRank: #${index + 1}\nSuccessful Meetings: ${performer.total_successful}\nScheduled Meetings: ${performer.total_scheduled}\nTotal Dials: ${performer.total_dials}\nProject: ${performer.project_name}\nTime Filter: ${timeFrame}\n\nRanking: Sorted by Successful → Scheduled → Dials`);
                }}
              >
                <div className="flex items-center justify-between">
                  {/* Left: Avatar, Name, Rank */}
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      {getRankIcon(index)}
                      <div className={`w-12 h-12 rounded-full ${performer.project_color} flex items-center justify-center text-white font-semibold text-sm transition-transform duration-200 hover:scale-110`}>
                        {initials}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-semibold text-gray-900">{performer.agent_name}</span>
                        {getRankBadge(index)}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <span>{performer.project_name}</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {performer.project_code}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Right: Metrics */}
                  <div className="flex items-center gap-6 text-right">
                    {/* Successful Meetings - Main Metric */}
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="text-sm text-gray-600 flex items-center justify-end gap-1 mb-1">
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        Successful Meetings
                      </div>
                      <div className="font-bold text-5xl text-green-600">
                        {performer.total_successful}
                      </div>
                    </div>

                    {/* Scheduled Meetings */}
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="text-xs text-gray-500 flex items-center justify-end gap-1">
                        <CalendarDays className="w-4 h-4 text-blue-500" />
                        Scheduled
                      </div>
                      <div className="font-bold text-lg text-blue-600">
                        {performer.total_scheduled}
                      </div>
                      <div className="text-xs text-gray-400">meetings</div>
                    </div>

                    {/* Dials */}
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="text-xs text-gray-500 flex items-center justify-end gap-1">
                        <Phone className="w-4 h-4 text-purple-500" />
                        Dials
                      </div>
                      <div className="font-bold text-lg text-purple-600">
                        {performer.total_dials.toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-400">calls</div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <div className="text-center py-8 text-gray-500">
            <TrendingUp className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p className="font-medium">No performance data available for {timeFrame.toLowerCase()} filter.</p>
            <p className="text-sm">Add some performance data to see top performers!</p>
          </div>
        )}

        <div className="mt-4 text-center text-xs text-gray-500">
          Click on performers for detailed metrics • Rankings: Successful → Scheduled → Dials • Filter: {timeFrame} • Real-time Supabase data
        </div>
      </CardContent>
    </Card>
  );
};

export default TopPerformers;
