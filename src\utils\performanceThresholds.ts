// Performance thresholds configuration
export const PERFORMANCE_THRESHOLDS = {
  dials: {
    daily: { good: 120, average: 80 },
    weekly: { good: 600, average: 400 },
    monthly: { good: 2400, average: 1600 }
  },
  connected: {
    daily: { good: 50, average: 35 },
    weekly: { good: 250, average: 175 },
    monthly: { good: 1000, average: 700 }
  },
  talkTime: { // in seconds
    daily: { good: 3600, average: 2100 }, // 1 hour = 3600 seconds
    weekly: { good: 18000, average: 10500 }, // 5 hours = 18000 seconds
    monthly: { good: 72000, average: 42000 } // 20 hours = 72000 seconds
  }
};

export type PerformanceStatus = 'good' | 'average' | 'low';
export type PerformanceMetric = 'dials' | 'connected' | 'talkTime';
export type TimeFrameKey = 'daily' | 'weekly' | 'monthly';

export const getPerformanceStatus = (
  metric: PerformanceMetric,
  value: number,
  timeFrame: string
): PerformanceStatus => {
  const thresholds = PERFORMANCE_THRESHOLDS[metric];
  
  // Map timeFrame to our keys
  let timeKey: TimeFrameKey = 'daily';
  if (timeFrame.toLowerCase().includes('week')) {
    timeKey = 'weekly';
  } else if (timeFrame.toLowerCase().includes('month')) {
    timeKey = 'monthly';
  }
  
  const { good, average } = thresholds[timeKey];
  
  if (value >= good) return 'good';
  if (value >= average) return 'average';
  return 'low';
};

export const getPerformanceColor = (status: PerformanceStatus): string => {
  const colors = {
    good: 'text-green-600',
    average: 'text-yellow-600',
    low: 'text-red-600'
  };
  return colors[status];
};

export const getPerformanceLabel = (status: PerformanceStatus): string => {
  const labels = {
    good: 'Good',
    average: 'Average',
    low: 'Low'
  };
  return labels[status];
};
