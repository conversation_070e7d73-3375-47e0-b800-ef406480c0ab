
import React, { createContext, useContext } from "react";
import { DashboardContextType } from "@/types/dashboard";
import { useDashboardState } from "@/hooks/useDashboardState";
import { useDashboardOperations } from "@/hooks/useDashboardOperations";
import { useAgentPerformanceOperations } from "@/hooks/dashboard/useAgentPerformanceOperations";

// Create the context
export const DashboardContext = createContext<DashboardContextType>({} as DashboardContextType);

export const DashboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const dashboardState = useDashboardState();
  const dashboardOperations = useDashboardOperations(
    dashboardState.projects,
    dashboardState.agents,
    dashboardState.metrics,
    dashboardState.selectedProject,
    dashboardState.timeFrame,
    dashboardState.selectedDate,
    dashboardState.selectedMonth,
    dashboardState.selectedWeek
  );
  
  const { 
    performanceData, 
    addPerformanceData, 
    updatePerformanceData,
    deletePerformanceData,
    getPerformanceByAgent,
    getPerformanceByProject,
    getAgentMetrics: getAgentPerformanceMetrics,
    getProjectMetrics: getProjectPerformanceMetrics,
    formatTalkTime,
    parseTalkTime,
    forceRefresh // Get the forceRefresh function from useAgentPerformanceOperations
  } = useAgentPerformanceOperations();

  const contextValue: DashboardContextType = {
    ...dashboardState,
    ...dashboardOperations,
    performanceData,
    addPerformanceData,
    updatePerformanceData,
    deletePerformanceData,
    getPerformanceByAgent,
    getPerformanceByProject,
    getAgentPerformanceMetrics,
    getProjectPerformanceMetrics,
    formatTalkTime,
    parseTalkTime,
    forceRefresh // Include forceRefresh in the context value
  };

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  );
};

// Custom hook to access the context
export const useDashboard = () => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error("useDashboard must be used within a DashboardProvider");
  }
  return context;
};
