import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AlertCircle, Check, Loader2, Upload } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AgentPerformanceInsert } from "@/hooks/dashboard/useAgentPerformanceOperations";
import { parseCSV, validateCSVRow, convertToSeconds, CSVRowData } from "@/utils/csvUtils";
import { correctCSVWithAI } from "@/services/geminiService";
import { useDashboard } from "@/contexts/DashboardContext";
import { Agent } from "@/types/dashboard";

interface BulkImportModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportComplete: () => void;
}

export const BulkImportModal = ({
  open,
  onOpenChange,
  onImportComplete
}: BulkImportModalProps) => {
  const { agents, addPerformanceData } = useDashboard();
  const [file, setFile] = useState<File | null>(null);
  const [errors, setErrors] = useState<string[]>([]);
  const [aiCorrecting, setAiCorrecting] = useState(false);
  const [parsing, setParsing] = useState(false);
  const [success, setSuccess] = useState(false);
  const [importingStatus, setImportingStatus] = useState<string>("");
  const [importProgress, setImportProgress] = useState<number>(0);

  const resetState = () => {
    setFile(null);
    setErrors([]);
    setAiCorrecting(false);
    setParsing(false);
    setSuccess(false);
    setImportingStatus("");
    setImportProgress(0);
  };

  const handleAutoCorrect = async () => {
    if (!file || errors.length === 0) return;
    
    setAiCorrecting(true);
    
    try {
      // Read the file content
      const fileContent = await file.text();
      
      // Use AI to correct the CSV
      const correctedCSV = await correctCSVWithAI(fileContent, errors.join("\n"));
      
      // Create a new corrected file
      const correctedFile = new File([correctedCSV], "corrected-import.csv", {
        type: "text/csv"
      });
      
      // Update the file state
      setFile(correctedFile);
      
      // Try parsing again
      await handleFileValidation(correctedFile);
    } catch (error) {
      setErrors(prev => [...prev, "AI correction failed. Please fix the file manually."]);
    } finally {
      setAiCorrecting(false);
    }
  };

  const getAgentIdByName = (agentName: string, agents: Agent[]): string | null => {
    const agent = agents.find(a => a.name.toLowerCase() === agentName.toLowerCase());
    return agent ? agent.id : null;
  };

  const handleFileValidation = async (selectedFile: File) => {
    setParsing(true);
    setErrors([]);
    
    try {
      const fileContent = await selectedFile.text();
      const rows = await parseCSV(fileContent);
      
      const validationErrors: string[] = [];
      
      rows.forEach((row, index) => {
        const error = validateCSVRow(row, index);
        if (error) validationErrors.push(error);
        
        // Check if agent exists
        const agentName = row["Agent Name"];
        if (agentName && !getAgentIdByName(agentName, agents)) {
          validationErrors.push(`Error in Row ${index + 1}: Agent '${agentName}' not found.`);
        }
      });
      
      if (validationErrors.length > 0) {
        setErrors(validationErrors);
      }
    } catch (error) {
      setErrors(["Failed to parse CSV file. Please check the format."]);
    } finally {
      setParsing(false);
    }
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) return;
    
    setFile(selectedFile);
    await handleFileValidation(selectedFile);
  };

  const processFileForImport = async () => {
    if (!file) return;
    
    setImportingStatus("Processing file...");
    setImportProgress(10);
    
    try {
      const fileContent = await file.text();
      const rows = await parseCSV(fileContent);
      
      setImportingStatus(`Preparing ${rows.length} records...`);
      setImportProgress(20);
      
      const totalRows = rows.length;
      let processedRows = 0;
      let successCount = 0;
      let failureCount = 0;
      
      for (const row of rows) {
        try {
          const agentId = getAgentIdByName(row["Agent Name"], agents);
          
          if (!agentId) {
            failureCount++;
            continue;
          }
          
          const agent = agents.find(a => a.id === agentId);
          if (!agent) {
            failureCount++;
            continue;
          }
          
          // Convert time values to seconds
          const hours = parseInt(row.Hour, 10) || 0;
          const minutes = parseInt(row.Minute, 10) || 0;
          const seconds = parseInt(row.Second, 10) || 0;
          const talkTimeMinutes = parseInt(row["Total Talk Time (mins)"], 10) || 0;
          
          const performance: AgentPerformanceInsert = {
            agent_id: agentId,
            project_id: agent.projectId,
            date: row.Date,
            dials: parseInt(row["Total Dials"], 10) || 0,
            connected: parseInt(row["Total Connected"], 10) || 0,
            talk_time: talkTimeMinutes * 60, // Convert minutes to seconds
            scheduled_meetings: parseInt(row["Scheduled Meetings"], 10) || 0,
            successful_meetings: parseInt(row["Successful Meetings"], 10) || 0,
            fop_scheduled: 0, // Default value for bulk import
            fop_successful: 0, // Default value for bulk import
          };
          
          const success = await addPerformanceData(performance);
          if (success) {
            successCount++;
          } else {
            failureCount++;
          }
        } catch (error) {
          console.error("Error processing row:", error);
          failureCount++;
        } finally {
          processedRows++;
          const progress = Math.round(20 + (processedRows / totalRows * 80));
          setImportProgress(progress);
          setImportingStatus(`Importing... ${processedRows}/${totalRows}`);
        }
      }
      
      setImportingStatus(`Import complete. Success: ${successCount}, Failed: ${failureCount}`);
      setImportProgress(100);
      setSuccess(true);
      
      // Close modal after a short delay
      setTimeout(() => {
        onImportComplete();
        onOpenChange(false);
        resetState();
      }, 1500);
      
    } catch (error) {
      console.error("Error during import:", error);
      setErrors(prev => [...prev, "Import failed. Please try again."]);
      setImportingStatus("Import failed");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Bulk Import Performance Data</DialogTitle>
          <DialogDescription>
            Upload a CSV file with performance data to import.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6">
          <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50">
            <input
              type="file"
              id="csvFile"
              accept=".csv"
              onChange={handleFileChange}
              className="hidden"
              disabled={parsing || aiCorrecting || importProgress > 0}
            />
            <label
              htmlFor="csvFile"
              className={`flex flex-col items-center justify-center w-full h-full cursor-pointer ${
                parsing || aiCorrecting || importProgress > 0 ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              <Upload className="h-8 w-8 text-gray-500 mb-2" />
              <span className="text-sm font-medium text-gray-700">
                {file ? file.name : "Click to upload CSV"}
              </span>
              <span className="text-xs text-gray-500 mt-1">
                Make sure it follows the sample format
              </span>
            </label>
          </div>

          {parsing && (
            <div className="flex items-center justify-center py-2">
              <Loader2 className="h-5 w-5 animate-spin mr-2" />
              <span>Validating file...</span>
            </div>
          )}

          {errors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Errors found</AlertTitle>
              <AlertDescription>
                <div className="max-h-32 overflow-y-auto text-xs">
                  {errors.map((error, i) => (
                    <div key={i} className="mb-1">{error}</div>
                  ))}
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2" 
                  onClick={handleAutoCorrect}
                  disabled={aiCorrecting}
                >
                  {aiCorrecting ? (
                    <>
                      <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      AI Correcting...
                    </>
                  ) : (
                    "Try AI Correction"
                  )}
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {importProgress > 0 && (
            <div className="space-y-2">
              <div className="text-sm text-center">{importingStatus}</div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div 
                  className="bg-green-600 h-2.5 rounded-full transition-all duration-500 ease-out" 
                  style={{ width: `${importProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          {success && (
            <Alert className="bg-green-50 border-green-200">
              <Check className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Success</AlertTitle>
              <AlertDescription className="text-green-700">
                Your data has been imported successfully.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="flex flex-col sm:flex-row sm:justify-between gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              onOpenChange(false);
              resetState();
            }}
            className="w-full sm:w-auto"
            disabled={parsing || aiCorrecting || importProgress > 0 && importProgress < 100}
          >
            Cancel
          </Button>
          <Button
            type="button"
            className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            onClick={processFileForImport}
            disabled={!file || errors.length > 0 || parsing || aiCorrecting || importProgress > 0}
          >
            Import Data
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
