
const GEMINI_API_KEY = "AIzaSyArr9TLQHEBgTbrs4UDe0iGDdCaCicbXfQ";
const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";

export interface GeminiRequest {
  contents: {
    parts: {
      text: string;
    }[];
  }[];
}

export interface GeminiResponse {
  candidates: {
    content: {
      parts: {
        text: string;
      }[];
    };
  }[];
}

export const correctCSVWithAI = async (csvContent: string, errorMessage: string): Promise<string> => {
  try {
    const prompt = `
I have a CSV file with the following headers:
"Date,Hour,Minute,Second,Total Dials,Total Connected,Total Talk Time (mins),Scheduled Meetings,Successful Meetings,Agent Name"

The validation error is: "${errorMessage}"

Here's the CSV content:
${csvContent}

Please fix the error in the CSV content and return ONLY the corrected CSV content.
`;

    const request: GeminiRequest = {
      contents: [
        {
          parts: [{ text: prompt }]
        }
      ]
    };

    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error("Failed to get response from Gemini API");
    }

    const data = await response.json() as GeminiResponse;
    const correctedCSV = data.candidates[0].content.parts[0].text;

    // Clean up the response - sometimes AI models add markdown code blocks
    return correctedCSV.replace(/```csv\n|```\n|```/g, '').trim();
  } catch (error) {
    console.error("Error using Gemini API:", error);
    throw error;
  }
};

export interface AIExportRequest {
  agentName?: string;
  projectName?: string;
  timeFrame: "Daily" | "Weekly" | "Monthly";
  startDate?: string;
  endDate?: string;
  selectedWeek?: string;
  selectedMonth?: string;
  scope: "agent" | "project" | "all";
  filters?: {
    project_name?: string;
    agent_name?: string;
    date?: string;
    date_range?: [string, string];
    week?: number;
    month?: string;
  };
}

// Get the actual Supabase database schema
const getSupabaseSchema = () => {
  return `
SUPABASE DATABASE SCHEMA:

Table: projects
- id (uuid, primary key)
- name (text) - e.g. "HungerBox", "DTSS", "Opex", "Dale Carnegie", "SIS NAG", "Ufirm", "Rare", "SIS 2.0"
- code (text) - e.g. "HB", "DTSS", "OPX", "DC"
- color (text)
- created_at (timestamp)
- updated_at (timestamp)

Table: agents  
- id (uuid, primary key)
- name (text) - e.g. "Rajesh Choudhari", "Kalpana Verma", "Vandita Tiwari"
- project_id (uuid, foreign key to projects.id)
- created_at (timestamp)
- updated_at (timestamp)

Table: agent_performance
- id (uuid, primary key)
- agent_id (uuid, foreign key to agents.id)
- project_id (uuid, foreign key to projects.id)
- date (date) - e.g. "2025-06-05"
- week (text) - e.g. "Week 1", "Week 2", "Week 3", "Week 4"
- dials (integer) - total number of calls made
- connected (integer) - number of calls that connected
- talk_time (integer) - talk time in seconds
- scheduled_meetings (integer) - meetings scheduled
- successful_meetings (integer) - meetings that were successful
- fop_scheduled (integer) - FOP meetings scheduled
- fop_successful (integer) - FOP meetings successful
- fop_projects (text array) - list of FOP project names
- success_rate (numeric) - calculated success rate
- created_at (timestamp)
- updated_at (timestamp)

RELATIONSHIPS:
- agents.project_id → projects.id
- agent_performance.agent_id → agents.id
- agent_performance.project_id → projects.id

IMPORTANT SCHEMA MAPPING RULES:
- When user mentions a project name, map to projects.name
- When user mentions an agent name, map to agents.name
- When user mentions a date, map to agent_performance.date
- When user mentions a week, map to agent_performance.week
- When user mentions a month, extract from agent_performance.date
- Performance metrics are in agent_performance table
`;
};

// Fuzzy matching function to find best match for names
const findBestMatch = (input: string, options: string[]): string | null => {
  if (!input || !options.length) return null;

  const inputLower = input.toLowerCase().trim();

  // Exact match first
  const exactMatch = options.find(option =>
    option.toLowerCase() === inputLower
  );
  if (exactMatch) return exactMatch;

  // Partial match (contains)
  const partialMatch = options.find(option =>
    option.toLowerCase().includes(inputLower) ||
    inputLower.includes(option.toLowerCase())
  );
  if (partialMatch) return partialMatch;

  // Abbreviation match (for projects like DC -> Dale Carnegie)
  const abbrevMatch = options.find(option => {
    const words = option.toLowerCase().split(' ');
    const initials = words.map(word => word[0]).join('');
    return initials === inputLower || inputLower === initials;
  });
  if (abbrevMatch) return abbrevMatch;

  // First name match (for agents)
  const firstNameMatch = options.find(option => {
    const firstName = option.toLowerCase().split(' ')[0];
    return firstName === inputLower || inputLower === firstName;
  });
  if (firstNameMatch) return firstNameMatch;

  return null;
};

export const parseExportRequestWithAI = async (
  userRequest: string,
  availableAgents: string[],
  availableProjects: string[]
): Promise<AIExportRequest> => {
  try {
    const schemaContext = getSupabaseSchema();
    
    const prompt = `
${schemaContext}

You are an expert AI assistant that understands natural language requests for data export. You must be schema-aware and map user requests to the correct database columns and values.

AVAILABLE DATA FROM DATABASE:
Available Projects: ${availableProjects.join(", ")}
Available Agents: ${availableAgents.join(", ")}

USER REQUEST: "${userRequest}"

SCHEMA MAPPING INSTRUCTIONS:
1. You have access to the above Supabase schema. Always map user requests to the correct columns and values.
2. When user mentions a project, map to projects.name column
3. When user mentions an agent, map to agents.name column  
4. When user mentions a date, map to agent_performance.date column
5. When user mentions a week, map to agent_performance.week column
6. When user mentions a month, filter by extracting month from agent_performance.date

PARSING RULES:
1. Accept ANY style of English - formal, informal, broken grammar, typos, abbreviations
2. Be extremely flexible with date formats: "5 june", "5th June", "5 jun", "june 5", "05/06", "5-6-2025", etc.
3. Use fuzzy matching for names:
   - "dc" or "dale" → "Dale Carnegie"
   - "dtss" → "DTSS"
   - "hungerbox" or "hunger" → "HungerBox"
   - "sis" → "SIS NAG"
   - "rajesh" → "Rajesh Choudhari"
   - "kalpana" → "Kalpana Verma"
   - "vandita" → "Vandita Tiwari"
   - Match partial names, first names, or abbreviations
4. Handle informal language: "get data", "download", "export", "show me", etc.
5. For current year 2025, assume dates without year are 2025
6. If user says "from X to Y" create a date range
7. If user mentions "week 1", "week 2" etc, use Weekly timeFrame and map to agent_performance.week
8. If user mentions month name only, use Monthly timeFrame and filter by month extracted from date

DATA VALIDATION:
- If user mentions a project, ensure it exists in available projects list
- If user mentions an agent, ensure it exists in available agents list
- If no exact match found, use fuzzy matching
- If still no match, set to null and let the application handle the error

RETURN ONLY THIS JSON FORMAT (no markdown, no explanation):
{
  "agentName": "exact_match_from_available_agents_or_null",
  "projectName": "exact_match_from_available_projects_or_null", 
  "timeFrame": "Daily|Weekly|Monthly",
  "startDate": "2025-MM-DD_or_null",
  "endDate": "2025-MM-DD_or_null",
  "selectedWeek": "Week_N_or_null",
  "selectedMonth": "Month_name_or_null",
  "scope": "agent|project|all",
  "filters": {
    "project_name": "mapped_to_projects.name_or_null",
    "agent_name": "mapped_to_agents.name_or_null",
    "date": "mapped_to_agent_performance.date_or_null",
    "date_range": ["start_date", "end_date"]_or_null,
    "week": "mapped_to_agent_performance.week_or_null",
    "month": "extracted_month_name_or_null"
  }
}

EXAMPLES WITH SCHEMA MAPPING:

"HungerBox all agent June performance" → {
  "agentName": null,
  "projectName": "HungerBox", 
  "timeFrame": "Monthly",
  "startDate": null,
  "endDate": null,
  "selectedWeek": null,
  "selectedMonth": "June",
  "scope": "project",
  "filters": {
    "project_name": "HungerBox",
    "agent_name": null,
    "date": null,
    "date_range": null,
    "week": null,
    "month": "June"
  }
}

"Vandita Tiwari data from DC for 5 to 6 June" → {
  "agentName": "Vandita Tiwari",
  "projectName": "Dale Carnegie",
  "timeFrame": "Daily", 
  "startDate": "2025-06-05",
  "endDate": "2025-06-06",
  "selectedWeek": null,
  "selectedMonth": null,
  "scope": "agent",
  "filters": {
    "project_name": "Dale Carnegie",
    "agent_name": "Vandita Tiwari",
    "date": null,
    "date_range": ["2025-06-05", "2025-06-06"],
    "week": null,
    "month": null
  }
}

"show me opex week 2" → {
  "agentName": null,
  "projectName": "Opex",
  "timeFrame": "Weekly",
  "startDate": null,
  "endDate": null, 
  "selectedWeek": "Week 2",
  "selectedMonth": null,
  "scope": "project",
  "filters": {
    "project_name": "Opex",
    "agent_name": null,
    "date": null,
    "date_range": null,
    "week": "Week 2",
    "month": null
  }
}

PARSE THIS REQUEST NOW WITH SCHEMA AWARENESS:
`;

    const request: GeminiRequest = {
      contents: [
        {
          parts: [{ text: prompt }]
        }
      ]
    };

    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Gemini API Error:", response.status, errorText);
      throw new Error(`API request failed: ${response.status}`);
    }

    const data = await response.json() as GeminiResponse;
    console.log("Schema-aware AI Response:", data);

    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error("Invalid response structure from AI");
    }

    const aiResponse = data.candidates[0].content.parts[0].text;
    console.log("Raw Schema-aware AI Response:", aiResponse);

    // Clean up the response and parse JSON
    let cleanResponse = aiResponse.replace(/```json\n?|```\n?|```/g, '').trim();

    // Try to extract JSON if there's extra text
    const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      cleanResponse = jsonMatch[0];
    }

    console.log("Cleaned Schema-aware Response:", cleanResponse);

    try {
      const parsedRequest = JSON.parse(cleanResponse) as AIExportRequest;
      console.log("Parsed Schema-aware Request:", parsedRequest);

      // Apply additional fuzzy matching validation
      let matchedAgentName = parsedRequest.agentName === "null" ? undefined : parsedRequest.agentName;
      let matchedProjectName = parsedRequest.projectName === "null" ? undefined : parsedRequest.projectName;

      // Try to find better matches using fuzzy matching as backup
      if (parsedRequest.agentName && parsedRequest.agentName !== "null") {
        const bestAgentMatch = findBestMatch(parsedRequest.agentName, availableAgents);
        if (bestAgentMatch) {
          matchedAgentName = bestAgentMatch;
        }
      }

      if (parsedRequest.projectName && parsedRequest.projectName !== "null") {
        const bestProjectMatch = findBestMatch(parsedRequest.projectName, availableProjects);
        if (bestProjectMatch) {
          matchedProjectName = bestProjectMatch;
        }
      }

      // Validate and sanitize the response
      const sanitizedResponse: AIExportRequest = {
        agentName: matchedAgentName,
        projectName: matchedProjectName,
        timeFrame: parsedRequest.timeFrame || "Daily",
        startDate: parsedRequest.startDate === "null" ? undefined : parsedRequest.startDate,
        endDate: parsedRequest.endDate === "null" ? undefined : parsedRequest.endDate,
        selectedWeek: parsedRequest.selectedWeek === "null" ? undefined : parsedRequest.selectedWeek,
        selectedMonth: parsedRequest.selectedMonth === "null" ? undefined : parsedRequest.selectedMonth,
        scope: parsedRequest.scope || "all",
        filters: parsedRequest.filters || {}
      };

      // Clean filters of null values
      if (sanitizedResponse.filters) {
        Object.keys(sanitizedResponse.filters).forEach(key => {
          if (sanitizedResponse.filters![key as keyof typeof sanitizedResponse.filters] === "null" || 
              sanitizedResponse.filters![key as keyof typeof sanitizedResponse.filters] === null) {
            delete sanitizedResponse.filters![key as keyof typeof sanitizedResponse.filters];
          }
        });
      }

      console.log("Final schema-aware sanitized response:", sanitizedResponse);
      return sanitizedResponse;
    } catch (parseError) {
      console.error("JSON Parse Error:", parseError);
      console.error("Failed to parse:", cleanResponse);
      throw new Error("AI response was not in expected format. Please try rephrasing with more specific details.");
    }

  } catch (error) {
    console.error("Error parsing export request with schema-aware AI:", error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes("API request failed")) {
        throw new Error("Unable to connect to AI service. Please check your internet connection and try again.");
      } else if (error.message.includes("AI response was not in expected format")) {
        throw new Error("AI could not process your request properly. Please try rephrasing with more specific details like project names, agent names, or date ranges.");
      } else if (error.message.includes("Invalid response structure")) {
        throw new Error("Received invalid response from AI service. Please try again.");
      }
    }

    throw new Error("Could not understand your request. Please try rephrasing it with more details (e.g., 'Export Vandita Tiwari data from HungerBox project for June 5-10').");
  }
};
