
import React, { useState } from "react";
import { Download, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import UniversalDropdown, { DropdownOption } from "@/components/ui/universal-dropdown";
import { useToast } from "@/hooks/use-toast";
import { useDashboard } from "@/contexts/DashboardContext";
import {
  ExportOptions,
  ExportScope,
  exportAllProjectsSummary,
  exportProjectWiseDetailed,
  exportAgentWise,
  exportCurrentView,
  exportSpecificProjectDetail,
} from "@/utils/exportUtils";
import ProjectSelectionModal from "./ProjectSelectionModal";
import AISmartExportModal from "./AISmartExportModal";

interface ExportPerformanceButtonProps {
  className?: string;
  variant?: "default" | "outline" | "secondary" | "ghost";
  size?: "default" | "sm" | "lg";
  selectedAgent?: string | null;
}

export const ExportPerformanceButton: React.FC<ExportPerformanceButtonProps> = ({
  className = "",
  variant = "outline",
  size = "default",
  selectedAgent: propSelectedAgent
}) => {
  const { toast } = useToast();
  const {
    projects,
    agents,
    performanceData,
    timeFrame,
    selectedDate,
    selectedWeek,
    selectedMonth,
    selectedProject
  } = useDashboard();

  const selectedAgent = propSelectedAgent;
  const [isExporting, setIsExporting] = useState(false);
  const [showProjectModal, setShowProjectModal] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);

  const handleExport = async (scope: ExportScope, specificProjectId?: string) => {
    setIsExporting(true);
    
    toast({
      title: "Export Started",
      description: "Preparing your performance data export...",
    });

    try {
      const exportOptions: ExportOptions = {
        scope,
        timeFrame,
        selectedDate,
        selectedWeek,
        selectedMonth,
        selectedProject,
        selectedAgent,
        specificProjectId
      };

      await new Promise(resolve => setTimeout(resolve, 500));

      switch (scope) {
        case "all-projects":
          exportAllProjectsSummary(projects, agents, performanceData, exportOptions);
          break;
        case "project-wise":
          exportProjectWiseDetailed(projects, agents, performanceData, exportOptions);
          break;
        case "agent-wise":
          exportAgentWise(projects, agents, performanceData, exportOptions);
          break;
        case "current-view":
          exportCurrentView(projects, agents, performanceData, exportOptions);
          break;
        case "specific-project":
          exportSpecificProjectDetail(projects, agents, performanceData, exportOptions);
          break;
        default:
          throw new Error("Invalid export scope");
      }

      toast({
        title: "Export Complete!",
        description: "Your performance data has been downloaded successfully.",
        variant: "default",
      });

    } catch (error) {
      console.error("Export failed:", error);
      toast({
        title: "Export Failed",
        description: "There was an error exporting your data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleSpecificProjectExport = (projectId: string) => {
    handleExport("specific-project", projectId);
  };

  const getExportDescription = (scope: ExportScope): string => {
    switch (scope) {
      case "all-projects":
        return "Summary data for all projects";
      case "project-wise":
        return "Detailed data grouped by project";
      case "agent-wise":
        return "Data grouped by agent across projects";
      case "current-view":
        if (selectedAgent) {
          return "Current agent's detailed performance";
        } else if (selectedProject && selectedProject !== "All") {
          return "Current project's detailed data";
        } else {
          return "Current view data";
        }
      case "specific-project":
        return "Select and export data for a specific project";
      default:
        return "";
    }
  };

  const isDataAvailable = performanceData && performanceData.length > 0;

  if (!isDataAvailable) {
    return (
      <Button
        variant={variant}
        size={size}
        className={`${className} opacity-50 cursor-not-allowed`}
        disabled
        title="No data available for export"
      >
        <Download className="h-4 w-4 mr-2" />
        Export Performance
      </Button>
    );
  }

  // Create export options for UniversalDropdown
  const exportOptions: DropdownOption[] = [
    {
      value: "ai-smart",
      label: "AI Smart Export",
      description: "Ask AI to export data in natural language",
      icon: <Sparkles className="h-4 w-4 text-purple-600" />,
      className: "bg-gradient-to-r from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100"
    },
    {
      value: "current-view",
      label: "Current View",
      description: getExportDescription("current-view")
    },
    {
      value: "all-projects",
      label: "Project Summary",
      description: getExportDescription("all-projects")
    },
    {
      value: "project-wise",
      label: "Project Wise (Detail)",
      description: getExportDescription("project-wise")
    },
    {
      value: "agent-wise",
      label: "Agent Wise",
      description: getExportDescription("agent-wise")
    },
    {
      value: "specific-project",
      label: "Specific Project Detail",
      description: getExportDescription("specific-project")
    }
  ];

  const handleExportOptionSelect = (option: DropdownOption) => {
    switch (option.value) {
      case "ai-smart":
        setShowAIModal(true);
        break;
      case "specific-project":
        setShowProjectModal(true);
        break;
      default:
        handleExport(option.value as ExportScope);
        break;
    }
  };

  return (
    <>
      <UniversalDropdown
        options={exportOptions}
        placeholder={isExporting ? "Exporting..." : "Export Performance"}
        onChange={handleExportOptionSelect}
        disabled={isExporting}
        className={className}
        triggerClassName={`${variant === "outline" ? "border border-input bg-background hover:bg-accent hover:text-accent-foreground" : ""} ${size === "sm" ? "h-8 px-3 text-xs" : size === "lg" ? "h-11 px-8" : "h-10 px-4 py-2"}`}
        customTrigger={
          <Button
            variant={variant}
            size={size}
            className={className}
            disabled={isExporting}
            title="Download performance data as CSV"
          >
            <Download className="h-4 w-4 mr-2" />
            {isExporting ? "Exporting..." : "Export Performance"}
          </Button>
        }
        showDescriptions={true}
      />

      <ProjectSelectionModal
        isOpen={showProjectModal}
        onClose={() => setShowProjectModal(false)}
        onConfirm={handleSpecificProjectExport}
        projects={projects}
      />

      <AISmartExportModal
        isOpen={showAIModal}
        onClose={() => setShowAIModal(false)}
      />
    </>
  );
};

export default ExportPerformanceButton;
