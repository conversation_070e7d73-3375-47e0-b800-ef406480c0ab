import React from "react";
import UniversalDropdown, { DropdownOption } from "@/components/ui/universal-dropdown";

interface MonthDropdownProps {
  selectedMonth: string;
  availableMonths: string[];
  onMonthSelect: (month: string) => void;
  isActive: boolean;
}

// Define which months have data (May and June as per specification)
const MONTHS_WITH_DATA = ["May", "Jun"];

export const MonthDropdown: React.FC<MonthDropdownProps> = ({
  selectedMonth,
  availableMonths,
  onMonthSelect,
  isActive,
}) => {
  // Create full year month list with "All Time" option for better UX
  const allMonths = [
    "All Time", "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const monthOptions: DropdownOption[] = allMonths.map((month) => ({
    value: month === "All Time" ? "All Time" : month.slice(0, 3), // Convert to short form for backend compatibility
    label: month,
  }));

  const handleMonthSelect = (option: DropdownOption) => {
    onMonthSelect(option.value);
  };

  // Display full month name or "All Time"
  const displayText = selectedMonth === "All Time" ? "All Time" :
    allMonths.find(month => month.slice(0, 3) === selectedMonth) || selectedMonth;

  // Find the current selected option
  const selectedOption = monthOptions.find(option => option.value === selectedMonth) || null;

  return (
    <UniversalDropdown
      options={monthOptions}
      placeholder="Select Month"
      value={selectedOption}
      onChange={handleMonthSelect}
      className="min-w-[140px]"
      minWidth="140px"
    />
  );
};

export default MonthDropdown;
