
import { useState, useEffect } from "react";
import { FilteredProject, TimeFrame, PerformanceView, WeekFrame } from "@/types/dashboard";

export const useUIState = (selectedProject: FilteredProject) => {
  const [timeFrame, setTimeFrame] = useState<TimeFrame>("Daily");
  // Default to "All Time" for comprehensive view of all available data
  const [selectedMonth, setSelectedMonth] = useState<string>("All Time");
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedWeek, setSelectedWeek] = useState<WeekFrame | null>(null);
  const [currentView, setCurrentView] = useState<'dashboard' | 'performance' | 'data-entry' | 'project-management' | 'agent-backlog'>('dashboard');
  const [performanceView, setPerformanceView] = useState<PerformanceView>("projects");
  const [isAgentPanelOpen, setIsAgentPanelOpen] = useState(false);
  const [isNewProjectModalOpen, setIsNewProjectModalOpen] = useState(false);
  const [isNewAgentModalOpen, setIsNewAgentModalOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Reset performance view when selected project changes
  useEffect(() => {
    if (selectedProject === "All") {
      setPerformanceView("projects");
    } else {
      setPerformanceView("agents");
    }
  }, [selectedProject]);

  return {
    timeFrame,
    setTimeFrame,
    selectedMonth,
    setSelectedMonth,
    selectedDate,
    setSelectedDate,
    selectedWeek,
    setSelectedWeek,
    currentView,
    setCurrentView,
    performanceView,
    setPerformanceView,
    isAgentPanelOpen,
    setIsAgentPanelOpen,
    isNewProjectModalOpen,
    setIsNewProjectModalOpen,
    isNewAgentModalOpen,
    setIsNewAgentModalOpen,
    isSidebarCollapsed,
    setIsSidebarCollapsed,
  };
};
