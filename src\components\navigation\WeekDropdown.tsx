import React from "react";
import UniversalDropdown, { DropdownOption } from "@/components/ui/universal-dropdown";
import { WeekFrame } from "@/types/dashboard";

interface WeekDropdownProps {
  selectedWeek: WeekFrame | null;
  availableWeeks: WeekFrame[];
  onWeekSelect: (week: WeekFrame) => void;
  isActive: boolean;
}

export const WeekDropdown: React.FC<WeekDropdownProps> = ({
  selectedWeek,
  availableWeeks,
  onWeekSelect,
  isActive,
}) => {
  // Clean up week labels - remove duplicate "Week" text and dots
  const weekOptions: DropdownOption[] = availableWeeks.map((week) => {
    // Extract just the number from "Week 1", "Week 2", etc.
    const weekNumber = week.replace('Week ', '');
    return {
      value: week,
      label: `Week ${weekNumber}`, // Clean format: "Week 1", "Week 2", etc.
    };
  });

  const handleWeekSelect = (option: DropdownOption) => {
    onWeekSelect(option.value as WeekFrame);
  };

  // Find the current selected option
  const selectedOption = weekOptions.find(option => option.value === selectedWeek) || null;

  // Clean trigger text - show just "Week X" or "Weekly" if none selected
  const triggerText = selectedWeek
    ? `Week ${selectedWeek.replace('Week ', '')}`
    : "Weekly";

  return (
    <UniversalDropdown
      options={weekOptions}
      placeholder="Weekly"
      value={selectedOption}
      onChange={handleWeekSelect}
      className="min-w-[120px]"
      minWidth="120px"
    />
  );
};

export default WeekDropdown;
