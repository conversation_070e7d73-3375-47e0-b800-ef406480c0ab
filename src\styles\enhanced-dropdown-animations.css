
/* Enhanced Dropdown Animations - Smooth and Beautiful */

/* Improved base dropdown animations */
@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes dropdownSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-8px) scale(0.96);
  }
}

/* Enhanced item stagger animation */
@keyframes itemFadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Apply to Radix Select components with improved timing */
[data-radix-select-content] {
  animation: dropdownSlideIn 0.2s cubic-bezier(0.16, 1, 0.3, 1) !important;
  transform-origin: top center !important;
}

[data-radix-select-content][data-state="closed"] {
  animation: dropdownSlideOut 0.15s cubic-bezier(0.4, 0, 1, 1) !important;
}

/* Select items with improved stagger timing */
[data-radix-select-item] {
  animation: itemFadeIn 0.2s cubic-bezier(0.16, 1, 0.3, 1) !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

[data-radix-select-item]:nth-child(1) { animation-delay: 0ms !important; }
[data-radix-select-item]:nth-child(2) { animation-delay: 20ms !important; }
[data-radix-select-item]:nth-child(3) { animation-delay: 40ms !important; }
[data-radix-select-item]:nth-child(4) { animation-delay: 60ms !important; }
[data-radix-select-item]:nth-child(5) { animation-delay: 80ms !important; }
[data-radix-select-item]:nth-child(6) { animation-delay: 100ms !important; }
[data-radix-select-item]:nth-child(7) { animation-delay: 120ms !important; }
[data-radix-select-item]:nth-child(8) { animation-delay: 140ms !important; }

/* Enhanced hover effects for select items */
[data-radix-select-item]:hover {
  transform: translateX(2px) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15) !important;
}

/* Popover content animations */
[data-radix-popover-content] {
  animation: dropdownSlideIn 0.2s cubic-bezier(0.16, 1, 0.3, 1) !important;
  transform-origin: var(--radix-popover-content-transform-origin) !important;
}

[data-radix-popover-content][data-state="closed"] {
  animation: dropdownSlideOut 0.15s cubic-bezier(0.4, 0, 1, 1) !important;
}

/* Enhanced focus styles */
[data-radix-select-item][data-highlighted] {
  background-color: rgb(239, 246, 255) !important;
  color: rgb(29, 78, 216) !important;
  transform: translateX(2px) !important;
}

/* Smooth trigger animations */
[data-radix-select-trigger] {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

[data-radix-select-trigger]:hover {
  border-color: rgb(147, 197, 253) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

[data-radix-select-trigger][data-state="open"] {
  border-color: rgb(59, 130, 246) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

/* Calendar specific animations */
.rdp {
  animation: dropdownSlideIn 0.2s cubic-bezier(0.16, 1, 0.3, 1) !important;
}

.rdp-day_button:hover {
  transform: scale(1.05) !important;
  transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Alert Dialog animations */
[data-radix-dialog-content] {
  animation: dropdownSlideIn 0.25s cubic-bezier(0.16, 1, 0.3, 1) !important;
}

/* Custom dropdown improvements */
.select-content-custom {
  background: white !important;
  border: 1px solid rgb(229, 231, 235) !important;
  border-radius: 8px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  animation: dropdownSlideIn 0.2s cubic-bezier(0.16, 1, 0.3, 1) !important;
  max-height: 200px !important;
  overflow-y: auto !important;
}

.select-item-custom {
  padding: 10px 14px !important;
  cursor: pointer !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  animation: itemFadeIn 0.2s cubic-bezier(0.16, 1, 0.3, 1) !important;
}

.select-item-custom:hover {
  background-color: rgb(239, 246, 255) !important;
  color: rgb(29, 78, 216) !important;
  transform: translateX(2px) !important;
}

/* Scrollbar styling for dropdowns */
.select-content-custom::-webkit-scrollbar {
  width: 6px !important;
}

.select-content-custom::-webkit-scrollbar-track {
  background: rgb(248, 250, 252) !important;
  border-radius: 3px !important;
}

.select-content-custom::-webkit-scrollbar-thumb {
  background: rgb(203, 213, 225) !important;
  border-radius: 3px !important;
  transition: background-color 0.2s ease !important;
}

.select-content-custom::-webkit-scrollbar-thumb:hover {
  background: rgb(148, 163, 184) !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  [data-radix-select-content] {
    max-height: 60vh !important;
  }
  
  [data-radix-select-item] {
    padding: 14px 18px !important;
    min-height: 48px !important;
  }
}

/* Ensure consistent z-index */
[data-radix-select-content],
[data-radix-popover-content],
[data-radix-dialog-content] {
  z-index: 9999 !important;
}

/* Enhanced Table Scrollbar Styling */
.table-container {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.table-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Top Scrollbar Styling */
.overflow-x-auto.overflow-y-hidden {
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #e2e8f0;
}

.overflow-x-auto.overflow-y-hidden::-webkit-scrollbar {
  height: 12px;
}

.overflow-x-auto.overflow-y-hidden::-webkit-scrollbar-track {
  background: #e2e8f0;
  border-radius: 6px;
}

.overflow-x-auto.overflow-y-hidden::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.overflow-x-auto.overflow-y-hidden::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Real-time Update Animations */
@keyframes fadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.deleting-row {
  animation: fadeOut 0.3s ease-out forwards;
}

.updating-row {
  background-color: #fef3c7 !important;
  transition: background-color 0.3s ease;
}

.updated-row {
  animation: fadeIn 0.3s ease-out;
  background-color: #d1fae5 !important;
}
