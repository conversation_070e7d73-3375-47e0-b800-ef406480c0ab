
import React, { useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import DataEntryView from "./DataEntryView";
import PerformanceHistoryView from "./PerformanceHistoryView";
import { useDashboard } from "@/contexts/DashboardContext";

const DataEntryTabs = () => {
  const { selectedProject, forceRefresh } = useDashboard();

  // Auto-refresh when switching to Performance History tab
  const handleTabChange = (value: string) => {
    if (value === "history") {
      console.log('📈 Switching to Performance History - refreshing data...');
      if (forceRefresh) {
        forceRefresh();
      }
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="entry" className="w-full" onValueChange={handleTabChange}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="entry">Daily Logs</TabsTrigger>
          <TabsTrigger value="history">Performance History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="entry" className="space-y-6">
          <DataEntryView />
        </TabsContent>
        
        <TabsContent value="history" className="space-y-6">
          <PerformanceHistoryView projectId={selectedProject} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DataEntryTabs;
