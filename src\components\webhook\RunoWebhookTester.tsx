
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';

export const RunoWebhookTester = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    agent_name: '<PERSON><PERSON><PERSON> Tiwari',
    dials: 25,
    connected: 15,
    talk_time: 1800, // 30 minutes in seconds
    scheduled_meetings: 3,
    successful_meetings: 2,
    fop_scheduled: 1,
    fop_successful: 1,
    date: new Date().toISOString().split('T')[0] // Today's date
  });

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: typeof value === 'string' && !isNaN(Number(value)) ? Number(value) : value
    }));
  };

  const testWebhook = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('https://rgcpzhwcbcfoiepxjqki.supabase.co/functions/v1/runo-webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: `Webhook test successful: ${result.message}`,
        });
      } else {
        toast({
          title: "Error",
          description: result.message || 'Webhook test failed',
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Webhook test error:', error);
      toast({
        title: "Error",
        description: "Failed to test webhook. Please check the console for details.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Runo Webhook Tester</CardTitle>
        <CardDescription>
          Test the Runo webhook endpoint with sample data. The webhook URL is:
          <br />
          <code className="text-sm bg-gray-100 px-2 py-1 rounded mt-1 inline-block">
            https://rgcpzhwcbcfoiepxjqki.supabase.co/functions/v1/runo-webhook
          </code>
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="agent_name">Agent Name</Label>
            <Input
              id="agent_name"
              value={formData.agent_name}
              onChange={(e) => handleInputChange('agent_name', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="date">Date</Label>
            <Input
              id="date"
              type="date"
              value={formData.date}
              onChange={(e) => handleInputChange('date', e.target.value)}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="dials">Dials</Label>
            <Input
              id="dials"
              type="number"
              value={formData.dials}
              onChange={(e) => handleInputChange('dials', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="connected">Connected</Label>
            <Input
              id="connected"
              type="number"
              value={formData.connected}
              onChange={(e) => handleInputChange('connected', e.target.value)}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="talk_time">Talk Time (seconds)</Label>
            <Input
              id="talk_time"
              type="number"
              value={formData.talk_time}
              onChange={(e) => handleInputChange('talk_time', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="scheduled_meetings">Scheduled Meetings</Label>
            <Input
              id="scheduled_meetings"
              type="number"
              value={formData.scheduled_meetings}
              onChange={(e) => handleInputChange('scheduled_meetings', e.target.value)}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="successful_meetings">Successful Meetings</Label>
            <Input
              id="successful_meetings"
              type="number"
              value={formData.successful_meetings}
              onChange={(e) => handleInputChange('successful_meetings', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="fop_scheduled">FOP Scheduled</Label>
            <Input
              id="fop_scheduled"
              type="number"
              value={formData.fop_scheduled}
              onChange={(e) => handleInputChange('fop_scheduled', e.target.value)}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="fop_successful">FOP Successful</Label>
          <Input
            id="fop_successful"
            type="number"
            value={formData.fop_successful}
            onChange={(e) => handleInputChange('fop_successful', e.target.value)}
            className="w-full"
          />
        </div>

        <Button 
          onClick={testWebhook} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? 'Testing...' : 'Test Webhook'}
        </Button>
      </CardContent>
    </Card>
  );
};
