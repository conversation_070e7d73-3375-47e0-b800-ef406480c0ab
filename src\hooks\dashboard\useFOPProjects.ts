
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Project } from '@/types/dashboard';
import { toast } from '@/hooks/use-toast';

export const useFOPProjects = () => {
  const [fopProjects, setFOPProjects] = useState<Project[]>([]);
  const [isLoadingFOPProjects, setIsLoadingFOPProjects] = useState(true);

  useEffect(() => {
    const fetchFOPProjects = async () => {
      try {
        setIsLoadingFOPProjects(true);
        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .order('name', { ascending: true });

        if (error) throw error;
        if (data) setFOPProjects(data);
      } catch (error) {
        console.error('Error fetching FOP projects:', error);
        toast({
          title: "Error",
          description: "Failed to load projects for FOP dropdown. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoadingFOPProjects(false);
      }
    };

    fetchFOPProjects();

    // Subscribe to realtime changes for projects
    const projectsSubscription = supabase
      .channel('fop-projects-changes')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'projects' 
      }, (payload) => {
        if (payload.eventType === 'INSERT') {
          setFOPProjects((current) => [...current, payload.new as Project].sort((a, b) => a.name.localeCompare(b.name)));
        } else if (payload.eventType === 'UPDATE') {
          setFOPProjects((current) => 
            current.map(project => project.id === payload.new.id ? payload.new as Project : project)
              .sort((a, b) => a.name.localeCompare(b.name))
          );
        } else if (payload.eventType === 'DELETE') {
          setFOPProjects((current) => 
            current.filter(project => project.id !== payload.old.id)
          );
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(projectsSubscription);
    };
  }, []);

  const refreshFOPProjects = async () => {
    try {
      setIsLoadingFOPProjects(true);
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('name', { ascending: true });

      if (error) throw error;
      if (data) setFOPProjects(data);
    } catch (error) {
      console.error('Error refreshing FOP projects:', error);
    } finally {
      setIsLoadingFOPProjects(false);
    }
  };

  return {
    fopProjects,
    isLoadingFOPProjects,
    refreshFOPProjects,
  };
};
