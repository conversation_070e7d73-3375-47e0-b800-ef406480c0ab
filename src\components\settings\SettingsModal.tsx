
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Settings, Activity, Bell, User } from "lucide-react";
import RealTimeDataTracker from "./RealTimeDataTracker";

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Settings
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="real-time" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="real-time" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Real-Time Data
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Profile
            </TabsTrigger>
            <TabsTrigger value="general" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              General
            </TabsTrigger>
          </TabsList>

          <TabsContent value="real-time" className="mt-6">
            <RealTimeDataTracker />
          </TabsContent>

          <TabsContent value="notifications" className="mt-6">
            <div className="text-center py-8 text-gray-500">
              Notification settings coming soon...
            </div>
          </TabsContent>

          <TabsContent value="profile" className="mt-6">
            <div className="text-center py-8 text-gray-500">
              Profile settings coming soon...
            </div>
          </TabsContent>

          <TabsContent value="general" className="mt-6">
            <div className="text-center py-8 text-gray-500">
              General settings coming soon...
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default SettingsModal;
