
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface RunoWebhookPayload {
  agent_name: string;
  inbound_dials?: number;
  outbound_dials?: number;
  inbound_connected?: number;
  outbound_connected?: number;
  inbound_talk_time?: number; // in seconds
  outbound_talk_time?: number; // in seconds
  scheduled_meetings?: number;
  successful_meetings?: number;
  fop_scheduled?: number;
  fop_successful?: number;
  date: string; // YYYY-MM-DD format
  project_name?: string;
  project_code?: string;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  if (req.method !== 'POST') {
    return new Response('Method not allowed', { 
      status: 405, 
      headers: corsHeaders 
    });
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse the incoming payload
    const payload: RunoWebhookPayload = await req.json();
    console.log('Received Runo webhook payload:', payload);

    // Validate required fields
    if (!payload.agent_name || !payload.date) {
      console.log('Missing required fields');
      
      // Log failed sync
      await supabase.from('sync_logs').insert({
        agent_name: payload.agent_name || 'Unknown',
        date: payload.date || new Date().toISOString().split('T')[0],
        sync_status: 'failed',
        message: 'Missing required fields: agent_name or date'
      });

      return new Response('Missing required fields: agent_name, date', {
        status: 400,
        headers: corsHeaders
      });
    }

    // Find the agent by name (exact match)
    const { data: agents, error: agentError } = await supabase
      .from('agents')
      .select('id, name, project_id')
      .ilike('name', payload.agent_name.trim())
      .limit(1);

    if (agentError) {
      console.error('Error finding agent:', agentError);
      
      await supabase.from('sync_logs').insert({
        agent_name: payload.agent_name,
        date: payload.date,
        sync_status: 'failed',
        message: 'Database error while finding agent'
      });

      return new Response('Error finding agent', {
        status: 500,
        headers: corsHeaders
      });
    }

    if (!agents || agents.length === 0) {
      console.log(`Agent not found: ${payload.agent_name}`);
      
      // Log that agent was not found
      await supabase.from('sync_logs').insert({
        agent_name: payload.agent_name,
        date: payload.date,
        sync_status: 'failed',
        message: `Agent name "${payload.agent_name}" not found in database. Check for exact name match.`
      });

      return new Response(`Agent not found: ${payload.agent_name}`, {
        status: 404,
        headers: corsHeaders
      });
    }

    const agent = agents[0];
    console.log(`Found agent: ${agent.name} (ID: ${agent.id})`);

    // Aggregate inbound and outbound data
    const totalDials = (payload.inbound_dials || 0) + (payload.outbound_dials || 0);
    const totalConnected = (payload.inbound_connected || 0) + (payload.outbound_connected || 0);
    const totalTalkTime = (payload.inbound_talk_time || 0) + (payload.outbound_talk_time || 0);

    // Calculate success rate
    const success_rate = (payload.scheduled_meetings || 0) > 0 
      ? ((payload.successful_meetings || 0) / (payload.scheduled_meetings || 0)) * 100 
      : 0;

    // Prepare the performance data
    const performanceData = {
      agent_id: agent.id,
      project_id: agent.project_id,
      date: payload.date,
      dials: totalDials,
      connected: totalConnected,
      talk_time: totalTalkTime,
      scheduled_meetings: payload.scheduled_meetings || 0,
      successful_meetings: payload.successful_meetings || 0,
      fop_scheduled: payload.fop_scheduled || 0,
      fop_successful: payload.fop_successful || 0,
      success_rate: success_rate,
      sync_source: 'runo_webhook',
      last_sync_at: new Date().toISOString()
    };

    console.log('Aggregated performance data:', performanceData);

    // Check if a record already exists for this agent and date
    const { data: existingRecord, error: checkError } = await supabase
      .from('agent_performance')
      .select('id, dials, connected, talk_time, scheduled_meetings, successful_meetings, fop_scheduled, fop_successful')
      .eq('agent_id', agent.id)
      .eq('date', payload.date)
      .limit(1);

    if (checkError) {
      console.error('Error checking existing record:', checkError);
      
      await supabase.from('sync_logs').insert({
        agent_name: payload.agent_name,
        date: payload.date,
        sync_status: 'failed',
        message: 'Error checking existing performance record'
      });

      return new Response('Error checking existing record', {
        status: 500,
        headers: corsHeaders
      });
    }

    let result;
    if (existingRecord && existingRecord.length > 0) {
      // Update existing record by incrementing values
      const existing = existingRecord[0];
      const updatedData = {
        dials: existing.dials + totalDials,
        connected: existing.connected + totalConnected,
        talk_time: existing.talk_time + totalTalkTime,
        scheduled_meetings: existing.scheduled_meetings + (payload.scheduled_meetings || 0),
        successful_meetings: existing.successful_meetings + (payload.successful_meetings || 0),
        fop_scheduled: existing.fop_scheduled + (payload.fop_scheduled || 0),
        fop_successful: existing.fop_successful + (payload.fop_successful || 0),
        success_rate: (existing.scheduled_meetings + (payload.scheduled_meetings || 0)) > 0 
          ? ((existing.successful_meetings + (payload.successful_meetings || 0)) / 
             (existing.scheduled_meetings + (payload.scheduled_meetings || 0))) * 100 
          : 0,
        sync_source: 'runo_webhook',
        last_sync_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('Updating existing record with incremented values:', updatedData);
      
      const { error: updateError } = await supabase
        .from('agent_performance')
        .update(updatedData)
        .eq('id', existing.id);

      if (updateError) {
        console.error('Error updating performance data:', updateError);
        
        await supabase.from('sync_logs').insert({
          agent_name: payload.agent_name,
          date: payload.date,
          sync_status: 'failed',
          message: 'Error updating performance data'
        });

        return new Response('Error updating performance data', {
          status: 500,
          headers: corsHeaders
        });
      }
      result = { action: 'updated', record_id: existing.id };
    } else {
      // Insert new record
      console.log('Inserting new performance record');
      const { data: insertData, error: insertError } = await supabase
        .from('agent_performance')
        .insert([performanceData])
        .select('id');

      if (insertError) {
        console.error('Error inserting performance data:', insertError);
        
        await supabase.from('sync_logs').insert({
          agent_name: payload.agent_name,
          date: payload.date,
          sync_status: 'failed',
          message: 'Error inserting new performance data'
        });

        return new Response('Error inserting performance data', {
          status: 500,
          headers: corsHeaders
        });
      }
      result = { action: 'inserted', record_id: insertData?.[0]?.id };
    }

    // Log successful sync
    await supabase.from('sync_logs').insert({
      agent_name: payload.agent_name,
      project_name: payload.project_name,
      date: payload.date,
      sync_status: 'success',
      message: `Data ${result.action} successfully. Total dials: ${totalDials}, Total connected: ${totalConnected}, Total talk time: ${totalTalkTime}s`
    });

    console.log('Successfully processed webhook:', result);

    return new Response(JSON.stringify({
      success: true,
      message: `Performance data ${result.action} successfully`,
      agent_name: payload.agent_name,
      date: payload.date,
      record_id: result.record_id,
      aggregated_data: {
        total_dials: totalDials,
        total_connected: totalConnected,
        total_talk_time: totalTalkTime
      }
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Webhook processing error:', error);
    
    // Log the error
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    await supabase.from('sync_logs').insert({
      agent_name: 'Unknown',
      date: new Date().toISOString().split('T')[0],
      sync_status: 'failed',
      message: `Webhook processing error: ${error.message}`
    });

    return new Response('Internal server error', {
      status: 500,
      headers: corsHeaders
    });
  }
});
