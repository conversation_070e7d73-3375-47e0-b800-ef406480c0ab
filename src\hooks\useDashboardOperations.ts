
import {
  Project,
  Agent,
  PerformanceMetric,
  TimeFrame,
  FilteredProject,
  WeekFrame
} from "@/types/dashboard";
import {
  getProjectById,
  getProjectAgents,
  getAgentById,
  getProjectMetrics,
  getTotalMetrics,
  getEnhancedFilteredMetrics,
  getAvailableMonths,
  getAvailableWeeks
} from "@/utils/dashboardUtils";

export const useDashboardOperations = (
  projects: Project[],
  agents: Agent[],
  metrics: PerformanceMetric[],
  selectedProject: FilteredProject,
  timeFrame: TimeFrame,
  selectedDate?: Date,
  selectedMonth?: string,
  selectedWeek?: WeekFrame | null
) => {
  const getProjectByIdOp = (id: string): Project | undefined => {
    return getProjectById(projects, id);
  };

  const getProjectAgentsOp = (projectId: string): Agent[] => {
    return getProjectAgents(agents, projectId);
  };

  const getAgentByIdOp = (id: string): Agent | undefined => {
    return getAgentById(agents, id);
  };

  const getProjectMetricsOp = (projectId: string): PerformanceMetric[] => {
    return getProjectMetrics(metrics, projectId);
  };

  const getFilteredMetricsOp = (): PerformanceMetric[] => {
    return getEnhancedFilteredMetrics(metrics, selectedProject, timeFrame, selectedDate, selectedMonth, selectedWeek);
  };

  const getTotalMetricsOp = () => {
    const filteredMetrics = getFilteredMetricsOp();
    return getTotalMetrics(filteredMetrics);
  };

  const getAvailableMonthsOp = (): string[] => {
    return getAvailableMonths(metrics);
  };

  const getAvailableWeeksOp = (month: string): WeekFrame[] => {
    return getAvailableWeeks(metrics, month);
  };

  // Get agents for a specific project
  const getAgentsByProjectId = (projectId: string): Agent[] => {
    if (projectId === "All" || projectId === "all") {
      return agents;
    }
    return agents.filter(agent => agent.projectId === projectId);
  };

  // Generate initials for an agent's avatar
  const getAgentInitials = (name: string): string => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Generate a random color for a project or agent with good contrast
  const getRandomColor = (): string => {
    const colors = [
      "bg-red-500", "bg-blue-500", "bg-green-500", "bg-purple-500",
      "bg-pink-500", "bg-indigo-500", "bg-teal-500", "bg-orange-500",
      "bg-emerald-500", "bg-cyan-500", "bg-violet-500", "bg-fuchsia-500",
      "bg-rose-500", "bg-amber-600", "bg-lime-600", "bg-sky-500"
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  return {
    getProjectById: getProjectByIdOp,
    getProjectAgents: getProjectAgentsOp,
    getAgentById: getAgentByIdOp,
    getProjectMetrics: getProjectMetricsOp,
    getFilteredMetrics: getFilteredMetricsOp,
    getTotalMetrics: getTotalMetricsOp,
    getAvailableMonths: getAvailableMonthsOp,
    getAvailableWeeks: getAvailableWeeksOp,
    getAgentsByProjectId,
    getAgentInitials,
    getRandomColor,
  };
};
