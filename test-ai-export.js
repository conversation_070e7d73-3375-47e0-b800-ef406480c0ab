// Test script for AI Export functionality
const GEMINI_API_KEY = "AIzaSyArr9TLQHEBgTbrs4UDe0iGDdCaCicbXfQ";
const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent";

const availableAgents = [
  "<PERSON><PERSON>", "Kalpana Verma", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> T<PERSON>wari", 
  "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>eh<PERSON> Rana", "<PERSON><PERSON><PERSON>", 
  "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"
];

const availableProjects = [
  "DTSS", "SIS NAG", "Opex", "Rare", "SIS 2.0", "Ufirm", 
  "Dale Carnegie", "Priya Living", "UniQ", "HungerBox"
];

async function testAIExport(userRequest) {
  try {
    const prompt = `
You are an expert AI assistant that understands natural language requests for data export. You must be very forgiving and flexible with user input.

AVAILABLE DATA:
Agents: ${availableAgents.join(", ")}
Projects: ${availableProjects.join(", ")}

USER REQUEST: "${userRequest}"

INSTRUCTIONS:
1. Accept ANY style of English - formal, informal, broken grammar, typos, abbreviations
2. Be extremely flexible with date formats: "5 june", "5th June", "5 jun", "june 5", "05/06", "5-6-2025", etc.
3. Use fuzzy matching for names:
   - "dc" or "dale" → "Dale Carnegie"
   - "dtss" → "DTSS" 
   - "hungerbox" or "hunger" → "HungerBox"
   - "sis" → "SIS NAG"
   - "rajesh" → "Rajesh Choudhari"
   - "kalpana" → "Kalpana Verma"
   - "aditi" → "Aditi Tiwari"
   - Match partial names, first names, or abbreviations
4. Handle informal language: "get data", "download", "export", "show me", etc.
5. For current year 2025, assume dates without year are 2025
6. If user says "from X to Y" create a date range
7. If user mentions "week 1", "week 2" etc, use Weekly timeFrame
8. If user mentions month name only, use Monthly timeFrame

RETURN ONLY THIS JSON FORMAT (no markdown, no explanation):
{
  "agentName": "exact_match_from_list_or_null",
  "projectName": "exact_match_from_list_or_null",
  "timeFrame": "Daily",
  "startDate": "2025-MM-DD",
  "endDate": "2025-MM-DD", 
  "selectedWeek": null,
  "selectedMonth": null,
  "scope": "agent_or_project_or_all"
}

PARSE THIS REQUEST NOW:
`;

    const request = {
      contents: [
        {
          role: "user",
          parts: [{ text: prompt }]
        }
      ]
    };

    console.log("Testing request:", userRequest);
    console.log("Sending to Gemini API...");

    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("API Error:", response.status, errorText);
      return;
    }

    const data = await response.json();
    console.log("Full AI Response:", JSON.stringify(data, null, 2));
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      console.error("Invalid response structure");
      return;
    }
    
    const aiResponse = data.candidates[0].content.parts[0].text;
    console.log("Raw AI Response Text:", aiResponse);

    // Clean up the response and parse JSON
    let cleanResponse = aiResponse.replace(/```json\n?|```\n?|```/g, '').trim();
    
    // Try to extract JSON if there's extra text
    const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      cleanResponse = jsonMatch[0];
    }
    
    console.log("Cleaned Response:", cleanResponse);

    try {
      const parsedRequest = JSON.parse(cleanResponse);
      console.log("Parsed Request:", JSON.stringify(parsedRequest, null, 2));
      return parsedRequest;
    } catch (parseError) {
      console.error("JSON Parse Error:", parseError);
      console.error("Failed to parse:", cleanResponse);
    }
    
  } catch (error) {
    console.error("Error:", error);
  }
}

// Test cases
const testCases = [
  "Get DTSS project data from 1st to 15th June",
  "rajesh data from dtss 5 to 6 june",
  "hungerbox week 1",
  "kalpana june data",
  "dc project performance",
  "aditi tiwari data june 5-10"
];

// Run tests
async function runTests() {
  for (const testCase of testCases) {
    console.log("\n" + "=".repeat(50));
    await testAIExport(testCase);
    console.log("=".repeat(50));
  }
}

// Uncomment to run tests
// runTests();

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testAIExport = testAIExport;
  window.runTests = runTests;
}
