
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useDashboard } from "@/contexts/DashboardContext";

export interface RealTimePerformer {
  agent_id: string;
  agent_name: string;
  project_name: string;
  project_code: string;
  total_scheduled: number;
  total_successful: number;
  total_dials: number;
  rank: number;
}

export const useRealTimePerformers = () => {
  const [performers, setPerformers] = useState<RealTimePerformer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { 
    timeFrame, 
    selectedDate, 
    selectedWeek, 
    selectedMonth,
    selectedProject 
  } = useDashboard();

  // Get date range based on current filters
  const getDateRange = () => {
    const now = new Date();
    let startDate: string;
    let endDate: string;

    switch (timeFrame) {
      case "Daily":
        const targetDate = selectedDate || now;
        startDate = targetDate.toISOString().split('T')[0];
        endDate = startDate;
        break;

      case "Weekly":
        if (selectedWeek) {
          const weekNumber = parseInt(selectedWeek.replace('Week ', '')) - 1;
          const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
          const weekStart = new Date(monthStart);
          weekStart.setDate(monthStart.getDate() + (weekNumber * 7));
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);
          
          startDate = weekStart.toISOString().split('T')[0];
          endDate = weekEnd.toISOString().split('T')[0];
        } else {
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay());
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);
          
          startDate = weekStart.toISOString().split('T')[0];
          endDate = weekEnd.toISOString().split('T')[0];
        }
        break;

      case "Monthly":
        if (selectedMonth && selectedMonth !== "All Time") {
          const monthIndex = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                             'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].indexOf(selectedMonth);
          if (monthIndex !== -1) {
            const monthStart = new Date(now.getFullYear(), monthIndex, 1);
            const monthEnd = new Date(now.getFullYear(), monthIndex + 1, 0);
            
            startDate = monthStart.toISOString().split('T')[0];
            endDate = monthEnd.toISOString().split('T')[0];
          } else {
            // All time - last 6 months
            const sixMonthsAgo = new Date(now);
            sixMonthsAgo.setMonth(now.getMonth() - 6);
            startDate = sixMonthsAgo.toISOString().split('T')[0];
            endDate = now.toISOString().split('T')[0];
          }
        } else {
          // Current month
          const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
          const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
          startDate = monthStart.toISOString().split('T')[0];
          endDate = monthEnd.toISOString().split('T')[0];
        }
        break;

      default:
        // Default to current month
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        startDate = monthStart.toISOString().split('T')[0];
        endDate = monthEnd.toISOString().split('T')[0];
    }

    return { startDate, endDate };
  };

  // Fetch top performers from Supabase
  const fetchTopPerformers = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const { startDate, endDate } = getDateRange();
      
      console.log('Fetching performers for date range:', { startDate, endDate, timeFrame, selectedProject });

      // Build the query
      let query = supabase
        .from('agent_performance')
        .select(`
          agent_id,
          project_id,
          dials,
          scheduled_meetings,
          successful_meetings,
          date,
          agents!inner(name),
          projects!inner(name, code)
        `)
        .gte('date', startDate)
        .lte('date', endDate);

      // Filter by project if not "All"
      if (selectedProject !== "All") {
        query = query.eq('project_id', selectedProject);
      }

      const { data, error: fetchError } = await query;

      if (fetchError) {
        console.error('Supabase fetch error:', fetchError);
        throw fetchError;
      }

      console.log('Raw data from Supabase:', data);

      if (!data || data.length === 0) {
        console.log('No performance data found for the selected period');
        setPerformers([]);
        return;
      }

      // Aggregate data by agent
      const agentMap = new Map<string, {
        agent_id: string;
        agent_name: string;
        project_name: string;
        project_code: string;
        total_scheduled: number;
        total_successful: number;
        total_dials: number;
      }>();

      data.forEach((record: any) => {
        const agentId = record.agent_id;
        const existing = agentMap.get(agentId);

        if (existing) {
          existing.total_scheduled += record.scheduled_meetings || 0;
          existing.total_successful += record.successful_meetings || 0;
          existing.total_dials += record.dials || 0;
        } else {
          agentMap.set(agentId, {
            agent_id: agentId,
            agent_name: record.agents?.name || 'Unknown Agent',
            project_name: record.projects?.name || 'Unknown Project',
            project_code: record.projects?.code || 'N/A',
            total_scheduled: record.scheduled_meetings || 0,
            total_successful: record.successful_meetings || 0,
            total_dials: record.dials || 0,
          });
        }
      });

      // Convert to array and apply the new 3-level sorting logic
      const sortedPerformers = Array.from(agentMap.values())
        .sort((a, b) => {
          // Primary sort: Total successful meetings (descending)
          if (a.total_successful !== b.total_successful) {
            return b.total_successful - a.total_successful;
          }
          
          // Secondary sort: Total scheduled meetings (descending)
          if (a.total_scheduled !== b.total_scheduled) {
            return b.total_scheduled - a.total_scheduled;
          }
          
          // Tertiary sort: Total dials (descending) - final tie-breaker
          return b.total_dials - a.total_dials;
        })
        .slice(0, 5) // Take top 5
        .map((performer, index) => ({
          ...performer,
          rank: index + 1
        }));

      console.log('Top 5 performers calculated with new sorting logic:', sortedPerformers);
      setPerformers(sortedPerformers);

    } catch (error) {
      console.error('Error fetching top performers:', error);
      setError('Failed to fetch performer data');
      setPerformers([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Effect to fetch data when filters change
  useEffect(() => {
    fetchTopPerformers();
  }, [timeFrame, selectedDate, selectedWeek, selectedMonth, selectedProject]);

  // Set up real-time subscription
  useEffect(() => {
    const channel = supabase
      .channel('top-performers-realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'agent_performance'
        },
        (payload) => {
          console.log('Real-time performance data change:', payload);
          // Refetch data when any performance record changes
          fetchTopPerformers();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [timeFrame, selectedDate, selectedWeek, selectedMonth, selectedProject]);

  return {
    performers,
    isLoading,
    error,
    refetch: fetchTopPerformers
  };
};
