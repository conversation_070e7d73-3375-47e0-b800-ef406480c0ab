
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Trophy, Award, Flame, Loader2, TrendingUp, Phone, Calendar, CheckCircle, Info, Eye } from "lucide-react";
import { useRealTimePerformers, RealTimePerformer } from "@/hooks/dashboard/useRealTimePerformers";
import { useDashboard } from "@/contexts/DashboardContext";

const RealTimeTopPerformers: React.FC = () => {
  const { performers, isLoading, error } = useRealTimePerformers();
  const { timeFrame, getAgentInitials, selectedDate, selectedWeek, selectedMonth } = useDashboard();

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="w-8 h-8 text-yellow-500" />;
      case 2:
        return <Award className="w-8 h-8 text-gray-400" />;
      case 3:
        return <Flame className="w-8 h-8 text-red-400" />;
      default:
        return <span className="w-8 text-lg font-semibold text-gray-400 text-center">#{rank}</span>;
    }
  };

  const getRankBadge = (rank: number) => {
    switch (rank) {
      case 1:
        return <Badge className="bg-yellow-200 text-yellow-800 text-xs font-semibold hover:bg-yellow-200">Star Performer</Badge>;
      case 2:
        return <Badge className="bg-indigo-100 text-indigo-700 text-xs font-semibold hover:bg-indigo-100">Runner Up</Badge>;
      case 3:
        return <Badge className="bg-red-50 text-red-700 text-xs font-semibold hover:bg-red-50">Top 3</Badge>;
      default:
        return <Badge className="bg-cyan-50 text-cyan-700 text-xs font-semibold hover:bg-cyan-50">Top 5</Badge>;
    }
  };

  const getProjectColor = (projectCode: string) => {
    const colors = [
      'bg-purple-500', 'bg-indigo-500', 'bg-red-500', 'bg-yellow-500', 
      'bg-pink-500', 'bg-blue-500', 'bg-green-500', 'bg-teal-500'
    ];
    const index = projectCode.charCodeAt(0) % colors.length;
    return colors[index];
  };

  const getFilterDescription = () => {
    switch (timeFrame) {
      case "Daily":
        return "Daily rankings";
      case "Weekly":
        return "Weekly rankings";
      case "Monthly":
        return "Monthly rankings";
      default:
        return "Real-time rankings";
    }
  };

  const getViewButtonText = () => {
    switch (timeFrame) {
      case "Daily":
        return "Daily View • Real-time Updates";
      case "Weekly":
        return "Weekly View • Real-time Updates";
      case "Monthly":
        return "Monthly View • Real-time Updates";
      default:
        return "Live View • Real-time Updates";
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-5xl mx-auto">
        <header className="flex justify-between items-center mb-8">
          <div className="flex items-center">
            <Trophy className="w-8 h-8 text-purple-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-800">Top 5 Performers</h1>
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            Live Data
          </div>
        </header>
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3 text-gray-500">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>Loading performance data for {timeFrame.toLowerCase()} view...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-5xl mx-auto">
        <header className="flex justify-between items-center mb-8">
          <div className="flex items-center">
            <Trophy className="w-8 h-8 text-purple-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-800">Top 5 Performers</h1>
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            Live Data
          </div>
        </header>
        <div className="text-center py-8">
          <div className="text-red-500 mb-2">⚠️ Data Fetch Error</div>
          <p className="text-gray-600 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-5xl mx-auto">
      {/* Header */}
      <header className="flex justify-between items-center mb-8">
        <div className="flex items-center">
          <Trophy className="w-8 h-8 text-purple-600 mr-3" />
          <h1 className="text-3xl font-bold text-gray-800">Top 5 Performers</h1>
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
          Live Data
        </div>
      </header>

      {/* Info Banner */}
      <div className="bg-blue-50 border border-blue-200 text-blue-700 px-6 py-4 rounded-xl mb-8 flex items-center justify-between">
        <div className="flex items-center">
          <Info className="w-5 h-5 mr-3" />
          <p className="text-sm">{getFilterDescription()} • Ranked by Successful Meetings</p>
        </div>
        <button className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg text-sm flex items-center transition duration-150">
          <Eye className="w-4 h-4 mr-2" />
          {getViewButtonText()}
        </button>
      </div>

      {/* Performers List */}
      <div className="space-y-6">
        {performers.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <TrendingUp className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p className="font-medium">No performance data available</p>
            <p className="text-sm">No performance records found for the selected {timeFrame.toLowerCase()} period</p>
          </div>
        ) : (
          performers.map((performer: RealTimePerformer) => {
            const initials = getAgentInitials(performer.agent_name);
            const projectColor = getProjectColor(performer.project_code);
            const isFirstPlace = performer.rank === 1;

            return (
              <div
                key={performer.agent_id}
                className={`p-6 flex items-center space-x-6 rounded-2xl transition-all duration-200 hover:shadow-lg cursor-pointer ${
                  isFirstPlace
                    ? 'bg-gradient-to-r from-yellow-100 to-yellow-200 border-2 border-yellow-300 shadow-md'
                    : 'bg-white border border-gray-200 shadow-sm hover:shadow-md'
                }`}
                onClick={() => {
                  alert(`${performer.agent_name} Performance Details:\n\nRank: #${performer.rank}\nProject: ${performer.project_name} (${performer.project_code})\nSuccessful Meetings: ${performer.total_successful}\nScheduled Meetings: ${performer.total_scheduled}\nTotal Dials: ${performer.total_dials}\nTime Period: ${timeFrame}\n\nRanking: Based on Successful Meetings Achievement`);
                }}
              >
                {/* Left Section - Rank Icon + Avatar + Name */}
                <div className="flex items-center space-x-4 flex-grow">
                  {getRankIcon(performer.rank)}
                  <div className={`w-12 h-12 rounded-full ${projectColor} flex items-center justify-center text-white font-semibold text-lg`}>
                    {initials}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h2 className="text-xl font-semibold text-gray-800">{performer.agent_name}</h2>
                      {getRankBadge(performer.rank)}
                    </div>
                    <p className="text-sm text-gray-600">
                      {performer.project_name} <span className="text-gray-400 mx-1">•</span> {performer.project_code}
                    </p>
                  </div>
                </div>

                {/* Right Section - Metrics */}
                <div className="flex space-x-8 text-right">
                  {/* Successful Meetings - Primary Metric */}
                  <div>
                    <p className="text-xs text-gray-500 flex items-center justify-end">
                      <CheckCircle className="w-4 h-4 mr-1 text-green-500" />
                      Successful
                    </p>
                    <div className="flex items-center justify-end">
                      <p className={`font-bold text-green-600 ${isFirstPlace ? 'text-3xl' : 'text-2xl'}`}>
                        {performer.total_successful}
                      </p>
                      {isFirstPlace && <CheckCircle className="w-6 h-6 ml-1 text-green-500" />}
                    </div>
                    <p className="text-xs text-gray-400">meetings</p>
                  </div>

                  {/* Scheduled Meetings */}
                  <div>
                    <p className="text-xs text-gray-500 flex items-center justify-end">
                      <Calendar className="w-4 h-4 mr-1 text-blue-500" />
                      Scheduled
                    </p>
                    <p className={`font-bold text-blue-600 ${isFirstPlace ? 'text-3xl' : 'text-2xl'}`}>
                      {performer.total_scheduled}
                    </p>
                    <p className="text-xs text-gray-400">meetings</p>
                  </div>

                  {/* Dials */}
                  <div>
                    <p className="text-xs text-gray-500 flex items-center justify-end">
                      <Phone className="w-4 h-4 mr-1 text-gray-800" />
                      Dials
                    </p>
                    <p className={`font-bold text-gray-800 ${isFirstPlace ? 'text-3xl' : 'text-2xl'}`}>
                      {performer.total_dials.toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default RealTimeTopPerformers;
