
import React, { useEffect, useState } from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import StatCard from "./StatCard";
import { Phone, Check, Clock, Calendar, CheckCircle, RefreshCw } from "lucide-react";
import { formatTime } from "@/lib/formatters";
import PerformanceCharts from "./PerformanceCharts";
import RealTimeTopPerformers from "./RealTimeTopPerformers";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

const DashboardView = () => {
  const { selectedProject, getProjectById, getTotalMetrics, forceRefresh } = useDashboard();
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const project = selectedProject !== "All" 
    ? getProjectById(selectedProject) 
    : { name: "Dashboard Overview", id: "all" };
  
  const metrics = getTotalMetrics();

  // Real-time subscription for dashboard updates
  useEffect(() => {
    console.log('🔄 [DASHBOARD] Setting up real-time subscription');
    
    const subscription = supabase
      .channel('dashboard-real-time')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'agent_performance' 
      }, (payload) => {
        console.log('🔄 [DASHBOARD] Real-time update received:', {
          eventType: payload.eventType,
          timestamp: new Date().toISOString()
        });

        // Force refresh to get latest aggregated data
        forceRefresh();
        
        toast({
          title: "📊 Dashboard Updated",
          description: "Dashboard metrics have been updated with latest data.",
          duration: 2000
        });
      })
      .subscribe((status) => {
        console.log('📡 [DASHBOARD] Real-time subscription status:', status);
      });

    return () => {
      console.log('🔌 [DASHBOARD] Cleaning up real-time subscription');
      supabase.removeChannel(subscription);
    };
  }, [forceRefresh]);

  const handleRefreshData = async () => {
    setIsRefreshing(true);
    try {
      console.log('🔄 [DASHBOARD] Manual refresh triggered');
      await forceRefresh();
      toast({
        title: "✅ Data Refreshed",
        description: "Dashboard has been updated with the latest data.",
      });
    } catch (error) {
      console.error('❌ [DASHBOARD] Error during refresh:', error);
      toast({
        title: "Error",
        description: "Failed to refresh data. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsRefreshing(false);
    }
  };
  
  return (
    <div className="space-y-6 max-w-7xl mx-auto">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Dashboard Overview {selectedProject !== "All" && `- ${project?.name}`}
            </h1>
            <p className="text-gray-600 text-lg">Your outbound calls summary at a glance, with AI-driven insights and trends.</p>
            {/* Real-time status indicator */}
            <div className="flex items-center gap-2 mt-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-gray-500">Live data - updates automatically</span>
            </div>
          </div>
          
          {/* Real-time refresh button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshData}
            disabled={isRefreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh Data'}
          </Button>
        </div>
      </div>
      
      {/* Enhanced KPI Cards with better spacing */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
        <StatCard
          title="Total Dials"
          value={metrics.totalDials.toLocaleString()}
          icon={<Phone className="text-purple-500" />}
          color="bg-purple-100"
          change={{ value: 11.6, isPositive: true }}
        />
        <StatCard
          title="Total Connected"
          value={metrics.totalConnected.toLocaleString()}
          icon={<Check className="text-green-500" />}
          color="bg-green-100"
          change={{ value: 18.9, isPositive: true }}
        />
        <StatCard
          title="Total Talk Time"
          value={formatTime(metrics.totalTalkTime)}
          icon={<Clock className="text-amber-500" />}
          color="bg-amber-100"
          change={{ value: 98.3, isPositive: true }}
        />
        <StatCard
          title="Scheduled Meetings"
          value={metrics.scheduledMeetings}
          icon={<Calendar className="text-purple-500" />}
          color="bg-purple-100"
          change={{ value: 76.6, isPositive: true }}
        />
        <StatCard
          title="Successful Meetings"
          value={metrics.successfulMeetings}
          icon={<CheckCircle className="text-green-500" />}
          color="bg-green-100"
          change={{ value: 58.0, isPositive: true }}
        />
      </div>

      {/* Performance Charts Section with enhanced spacing */}
      <div className="mb-8 space-y-6">
        <PerformanceCharts />
      </div>

      {/* Real-time Top Performers Section */}
      <div className="mb-6">
        <RealTimeTopPerformers />
      </div>
    </div>
  );
};

export default DashboardView;
