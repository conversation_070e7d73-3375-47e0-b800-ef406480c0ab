import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface AgentPerformanceRecord {
  id: string;
  agent_id: string;
  project_id: string;
  date: string;
  dials: number;
  connected: number;
  talk_time: number;
  scheduled_meetings: number;
  successful_meetings: number;
  created_at: string;
  updated_at: string;
}

const DataInspector: React.FC = () => {
  const [data, setData] = useState<AgentPerformanceRecord[]>([]);
  const [agents, setAgents] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState('2025-07-01');

  const fetchAgentsAndProjects = async () => {
    try {
      const [agentsResult, projectsResult] = await Promise.all([
        supabase.from('agents').select('*'),
        supabase.from('projects').select('*')
      ]);

      if (agentsResult.data) setAgents(agentsResult.data);
      if (projectsResult.data) setProjects(projectsResult.data);

      console.log('👥 [DEBUG] Agents:', agentsResult.data);
      console.log('📁 [DEBUG] Projects:', projectsResult.data);
    } catch (error) {
      console.error('❌ [DEBUG] Error fetching agents/projects:', error);
    }
  };

  const fetchData = async () => {
    setLoading(true);
    try {
      console.log('🔍 [DEBUG] Fetching data for date:', selectedDate);

      const { data: rawData, error } = await supabase
        .from('agent_performance')
        .select(`
          id,
          agent_id,
          project_id,
          date,
          dials,
          connected,
          talk_time,
          scheduled_meetings,
          successful_meetings,
          created_at,
          updated_at
        `)
        .eq('date', selectedDate)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ [DEBUG] Error fetching data:', error);
        return;
      }

      console.log('✅ [DEBUG] Fetched data:', rawData);
      setData(rawData || []);
    } catch (error) {
      console.error('❌ [DEBUG] Exception:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllData = async () => {
    setLoading(true);
    try {
      console.log('🔍 [DEBUG] Fetching ALL data');
      
      const { data: rawData, error } = await supabase
        .from('agent_performance')
        .select(`
          id,
          agent_id,
          project_id,
          date,
          dials,
          connected,
          talk_time,
          scheduled_meetings,
          successful_meetings,
          created_at,
          updated_at
        `)
        .order('date', { ascending: false })
        .limit(50);

      if (error) {
        console.error('❌ [DEBUG] Error fetching all data:', error);
        return;
      }

      console.log('✅ [DEBUG] Fetched all data:', rawData);
      setData(rawData || []);
    } catch (error) {
      console.error('❌ [DEBUG] Exception:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAgentsAndProjects();
    fetchData();
  }, [selectedDate]);

  const getAgentName = (agentId: string) => {
    const agent = agents.find(a => a.id === agentId);
    return agent ? agent.name : 'Unknown Agent';
  };

  const getProjectName = (projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    return project ? project.name : 'Unknown Project';
  };

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <CardTitle>🔍 Data Inspector - Agent Performance Records</CardTitle>
        <div className="flex gap-4 items-center">
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="px-3 py-2 border rounded"
          />
          <Button onClick={fetchData} disabled={loading}>
            Fetch Date Data
          </Button>
          <Button onClick={fetchAllData} disabled={loading} variant="outline">
            Fetch All Data (50 records)
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div>Loading...</div>
        ) : (
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              Found {data.length} records for {selectedDate}
            </div>
            
            {data.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No data found for the selected date
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full text-sm border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border border-gray-300 px-2 py-1">Date</th>
                      <th className="border border-gray-300 px-2 py-1">Agent Name</th>
                      <th className="border border-gray-300 px-2 py-1">Project Name</th>
                      <th className="border border-gray-300 px-2 py-1">Dials</th>
                      <th className="border border-gray-300 px-2 py-1">Connected</th>
                      <th className="border border-gray-300 px-2 py-1">Talk Time</th>
                      <th className="border border-gray-300 px-2 py-1">Scheduled</th>
                      <th className="border border-gray-300 px-2 py-1">Successful</th>
                      <th className="border border-gray-300 px-2 py-1">Created</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((record) => (
                      <tr key={record.id}>
                        <td className="border border-gray-300 px-2 py-1">{record.date}</td>
                        <td className="border border-gray-300 px-2 py-1">
                          <div className="font-semibold">{getAgentName(record.agent_id)}</div>
                          <div className="font-mono text-xs text-gray-500">
                            {record.agent_id.substring(0, 8)}...
                          </div>
                        </td>
                        <td className="border border-gray-300 px-2 py-1">
                          <div className="font-semibold">{getProjectName(record.project_id)}</div>
                          <div className="font-mono text-xs text-gray-500">
                            {record.project_id.substring(0, 8)}...
                          </div>
                        </td>
                        <td className="border border-gray-300 px-2 py-1 text-center">{record.dials}</td>
                        <td className="border border-gray-300 px-2 py-1 text-center">{record.connected}</td>
                        <td className="border border-gray-300 px-2 py-1 text-center">{record.talk_time}</td>
                        <td className="border border-gray-300 px-2 py-1 text-center">{record.scheduled_meetings}</td>
                        <td className="border border-gray-300 px-2 py-1 text-center">{record.successful_meetings}</td>
                        <td className="border border-gray-300 px-2 py-1 text-xs">
                          {new Date(record.created_at).toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DataInspector;
