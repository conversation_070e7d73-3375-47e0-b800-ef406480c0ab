import React from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StarIconProps {
  className?: string;
  size?: number;
  filled?: boolean;
}

export const StarIcon: React.FC<StarIconProps> = ({ 
  className, 
  size = 16, 
  filled = true 
}) => {
  return (
    <Star 
      size={size}
      className={cn(
        filled ? 'fill-yellow-400 text-yellow-400' : 'text-yellow-400',
        'inline-block',
        className
      )}
    />
  );
};

export default StarIcon;
