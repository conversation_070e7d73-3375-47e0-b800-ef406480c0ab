
import { useState, useEffect } from "react";
import { PerformanceMetric } from "@/types/dashboard";
import { useAgentPerformanceOperations, AgentPerformance } from "./useAgentPerformanceOperations";

export const useMetricsOperations = () => {
  const { performanceData, getAgentMetrics, getProjectMetrics } = useAgentPerformanceOperations();
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);

  // Convert AgentPerformance to PerformanceMetric format
  useEffect(() => {
    const convertedMetrics: PerformanceMetric[] = performanceData.map((item: AgentPerformance) => ({
      id: item.id,
      agentId: item.agent_id,
      projectId: item.project_id,
      date: item.date,
      week: item.week,
      dials: item.dials,
      connected: item.connected,
      talkTime: item.talk_time,
      scheduledMeetings: item.scheduled_meetings,
      successfulMeetings: item.successful_meetings,
      fopScheduled: item.fop_scheduled,
      fopSuccessful: item.fop_successful,
      successRate: item.success_rate,
    }));

    setMetrics(convertedMetrics);
  }, [performanceData]);

  return {
    metrics,
    getAgentMetrics,
    getProjectMetrics,
  };
};
