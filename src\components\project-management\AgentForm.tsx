
import React, { useEffect } from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import UniversalDropdown, { DropdownOption } from "@/components/ui/universal-dropdown";
import { useForm, Controller } from "react-hook-form";

type AgentFormProps = {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  projectId?: string;
};

type AgentFormData = {
  name: string;
  projectId: string;
};

const AgentForm: React.FC<AgentFormProps> = ({ isOpen, setIsOpen, projectId }) => {
  const { addAgent, projects } = useDashboard();
  const { register, handleSubmit, formState: { errors }, reset, control, setValue } = useForm<AgentFormData>();

  // Convert projects to dropdown options
  const projectOptions: DropdownOption[] = projects
    .filter(project => project.id !== "all")
    .map(project => ({
      value: project.id,
      label: project.name
    }));

  // Set the projectId if provided
  useEffect(() => {
    if (projectId) {
      setValue("projectId", projectId);
    }
  }, [projectId, setValue]);

  const onSubmit = async (data: AgentFormData) => {
    // Create a new agent with the form data
    const newAgent = {
      name: data.name,
      projectId: data.projectId,
    };

    const success = await addAgent(newAgent);
    if (success) {
      setIsOpen(false);
      reset();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add New Agent</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Agent Name</Label>
            <Input
              id="name"
              placeholder="Enter agent name"
              {...register("name", { required: "Agent name is required" })}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="projectId">Project</Label>
            <Controller
              name="projectId"
              control={control}
              rules={{ required: "Project is required" }}
              render={({ field }) => {
                const selectedOption = projectOptions.find(option => option.value === field.value) || null;
                return (
                  <UniversalDropdown
                    options={projectOptions}
                    placeholder="Select a project"
                    value={selectedOption}
                    onChange={(option) => field.onChange(option.value)}
                    className="w-full"
                  />
                );
              }}
            />
            {errors.projectId && (
              <p className="text-sm text-red-500">{errors.projectId.message}</p>
            )}
          </div>
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button type="submit">Add Agent</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AgentForm;
