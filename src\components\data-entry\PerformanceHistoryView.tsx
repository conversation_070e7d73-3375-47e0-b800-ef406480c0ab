import React, { useState, useEffect } from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { format, isWithinInterval, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, addWeeks } from "date-fns";
import { Edit, Trash2, Save, X, Calendar, RefreshCw, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { TimeInput } from "@/components/ui/time-input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import UniversalDropdown, { DropdownOption } from "@/components/ui/universal-dropdown";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { AgentPerformance } from "@/hooks/dashboard/useAgentPerformanceOperations";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface PerformanceHistoryViewProps {
  agentId?: string;
  projectId?: string;
}

const PerformanceHistoryView: React.FC<PerformanceHistoryViewProps> = ({
  agentId,
  projectId,
}) => {
  const {
    performanceData,
    updatePerformanceData,
    deletePerformanceData,
    formatTalkTime,
    parseTalkTime,
    getProjectById,
    getAgentById,
    projects,
    agents,
    timeFrame,
    selectedDate,
    selectedWeek,
    selectedMonth,
    forceRefresh
  } = useDashboard();
  const { toast } = useToast();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editData, setEditData] = useState<Partial<AgentPerformance>>({});
  const [selectedFopProjects, setSelectedFopProjects] = useState<string[]>([]);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());
  const [dataFetchError, setDataFetchError] = useState<string | null>(null);

  // Auto-refresh data when component mounts or when timeframe changes
  useEffect(() => {
    console.log('📈 Performance History View mounted/updated - refreshing data...');
    handleRefresh();
  }, [timeFrame, selectedDate, selectedWeek, selectedMonth]);

  // Handle manual refresh with enhanced error handling
  const handleRefresh = async () => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    setDataFetchError(null);
    console.log('🔄 Manually refreshing Performance History data...');
    
    try {
      if (forceRefresh) {
        await forceRefresh();
        console.log('✅ Performance History data refreshed successfully');
        
        // Validate that we have the expected data
        if (performanceData.length === 0) {
          console.warn('⚠️ No performance data found after refresh');
        }
      }
    } catch (error) {
      console.error('❌ Error refreshing Performance History:', error);
      setDataFetchError(`Failed to refresh data: ${error.message || 'Unknown error'}`);
      toast({
        title: "Refresh Failed",
        description: "Unable to refresh data. Please check your connection and try again.",
        variant: "destructive"
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Generate week options (Week 1-5)
  const weekOptions = Array.from({ length: 5 }, (_, i) => `Week ${i + 1}`);

  // Convert to dropdown options
  const weekDropdownOptions: DropdownOption[] = weekOptions.map(week => ({
    value: week,
    label: week
  }));

  const projectDropdownOptions: DropdownOption[] = projects
    .filter(project => project.id !== "all")
    .map(project => ({
      value: project.id,
      label: project.name
    }));

  const agentDropdownOptions: DropdownOption[] = agents.map(agent => ({
    value: agent.id,
    label: agent.name
  }));

  const fopProjectDropdownOptions: DropdownOption[] = projects
    .filter(project => !selectedFopProjects.includes(project.id))
    .map(project => ({
      value: project.id,
      label: project.name
    }));

  // Helper function to get week date range from WeekFrame
  const getWeekDateRange = (weekFrame: string) => {
    // For weekly filtering, we'll use a more flexible approach
    // that looks at the actual week field in the data rather than calculating dates
    // This matches how the data is structured in the performance entries
    const weekNumber = parseInt(weekFrame.replace('Week ', ''));

    // If we have a selected month, use that month's context
    if (selectedMonth && selectedMonth !== "All Time") {
      const currentYear = new Date().getFullYear();
      const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
      const monthIndex = monthNames.indexOf(selectedMonth);

      if (monthIndex !== -1) {
        const monthStart = new Date(currentYear, monthIndex, 1);
        const weekStart = addWeeks(startOfWeek(monthStart), weekNumber - 1);
        const weekEnd = endOfWeek(weekStart);
        return { weekStart, weekEnd };
      }
    }

    // Fallback to current month
    const monthStart = startOfMonth(new Date());
    const weekStart = addWeeks(startOfWeek(monthStart), weekNumber - 1);
    const weekEnd = endOfWeek(weekStart);
    return { weekStart, weekEnd };
  };

  // Enhanced filtering with comprehensive date matching and logging
  const filteredData = performanceData.filter(item => {
    console.log('🔍 Filtering item:', {
      itemId: item.id,
      itemDate: item.date,
      itemWeek: item.week,
      agentId: item.agent_id,
      projectId: item.project_id,
      currentFilters: { timeFrame, selectedDate, selectedWeek, selectedMonth, agentId, projectId }
    });

    const itemDate = new Date(item.date);

    // Apply prop-based filters first
    if (agentId && item.agent_id !== agentId) {
      console.log('❌ Filtered out by agentId:', item.agent_id, 'expected:', agentId);
      return false;
    }
    if (projectId && item.project_id !== projectId) {
      console.log('❌ Filtered out by projectId:', item.project_id, 'expected:', projectId);
      return false;
    }

    // Apply global date filters based on timeFrame with enhanced matching
    switch (timeFrame) {
      case "Daily":
        if (selectedDate) {
          const matchesDate = isWithinInterval(itemDate, {
            start: startOfDay(selectedDate),
            end: endOfDay(selectedDate)
          });
          
          // Also check exact date string match for precision
          const exactDateMatch = item.date === selectedDate.toISOString().split('T')[0];
          
          console.log('📅 Daily filter check:', {
            itemDate: item.date,
            selectedDate: selectedDate.toISOString().split('T')[0],
            intervalMatch: matchesDate,
            exactMatch: exactDateMatch,
            finalResult: matchesDate || exactDateMatch
          });
          
          return matchesDate || exactDateMatch;
        }
        // If no specific date selected, show today's data
        const today = new Date().toISOString().split('T')[0];
        const matchesToday = item.date === today || isWithinInterval(itemDate, {
          start: startOfDay(new Date()),
          end: endOfDay(new Date())
        });
        
        console.log('📅 Daily filter (today):', {
          itemDate: item.date,
          today,
          matches: matchesToday
        });
        
        return matchesToday;

      case "Weekly":
        if (selectedWeek) {
          // Primary: Direct week field match (most reliable)
          const directWeekMatch = item.week === selectedWeek;
          
          // Secondary: Date range calculation (fallback)
          const { weekStart, weekEnd } = getWeekDateRange(selectedWeek);
          const dateRangeMatch = isWithinInterval(itemDate, {
            start: startOfDay(weekStart),
            end: endOfDay(weekEnd)
          });
          
          console.log('📅 Weekly filter check:', {
            itemWeek: item.week,
            selectedWeek,
            directMatch: directWeekMatch,
            weekRange: { start: weekStart.toISOString().split('T')[0], end: weekEnd.toISOString().split('T')[0] },
            dateRangeMatch,
            finalResult: directWeekMatch || dateRangeMatch
          });
          
          return directWeekMatch || dateRangeMatch;
        }
        // If no specific week selected, show current week's data
        const currentWeekMatch = isWithinInterval(itemDate, {
          start: startOfWeek(new Date()),
          end: endOfWeek(new Date())
        });
        
        console.log('📅 Weekly filter (current week):', {
          itemDate: item.date,
          matches: currentWeekMatch
        });
        
        return currentWeekMatch;

      case "Monthly":
        if (selectedMonth && selectedMonth !== "All Time") {
          const itemMonthName = format(itemDate, 'MMM');
          const monthMatch = itemMonthName === selectedMonth;
          
          console.log('📅 Monthly filter check:', {
            itemMonth: itemMonthName,
            selectedMonth,
            matches: monthMatch
          });
          
          return monthMatch;
        }
        // If "All Time" or no month selected, show all data
        console.log('📅 Monthly filter (all time): showing all data');
        return true;

      default:
        // For other timeframes (Quarterly, Yearly), show all data
        console.log('📅 Default filter: showing all data');
        return true;
    }
  }).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()); // Sort by date descending

  console.log('📊 Filtered data result:', {
    totalItems: performanceData.length,
    filteredItems: filteredData.length,
    filters: { timeFrame, selectedDate, selectedWeek, selectedMonth, agentId, projectId }
  });

  const startEdit = (item: AgentPerformance) => {
    setEditingId(item.id);
    setEditData({
      date: item.date,
      week: item.week,
      agent_id: item.agent_id,
      project_id: item.project_id,
      dials: item.dials,
      connected: item.connected,
      talk_time: item.talk_time,
      scheduled_meetings: item.scheduled_meetings,
      successful_meetings: item.successful_meetings,
      fop_scheduled: item.fop_scheduled,
      fop_successful: item.fop_successful,
      fop_projects: item.fop_projects || [],
    });
    setSelectedFopProjects(item.fop_projects || []);
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditData({});
    setSelectedFopProjects([]);
  };

  const saveEdit = async (id: string) => {
    if (isUpdating) return;
    
    setIsUpdating(true);
    console.log('💾 Saving edit for ID:', id);
    console.log('📝 Edit data:', editData);
    
    try {
      // Prepare the update data with proper structure
      const updateData = {
        ...editData,
        fop_projects: selectedFopProjects,
        updated_at: new Date().toISOString()
      };
      
      console.log('📤 Prepared update data:', updateData);
      
      const success = await updatePerformanceData(id, updateData);
      
      if (success) {
        console.log('✅ Update successful, clearing edit state');

        // Clear editing state immediately for instant UI feedback
        setEditingId(null);
        setEditData({});
        setSelectedFopProjects([]);

        // Show success message
        toast({
          title: "✅ Updated Successfully",
          description: "Changes synced in real-time across all tabs and users.",
          variant: "default"
        });

      } else {
        throw new Error('Update failed');
      }
    } catch (error) {
      console.error('❌ Error updating performance data:', error);
      toast({
        title: "Update Failed",
        description: `Failed to update performance data: ${error.message || 'Unknown error'}. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // ENHANCED DELETE FUNCTION with instant UI feedback
  const deleteEntry = async (id: string) => {
    if (deletingIds.has(id)) return;
    
    console.log(`🗑️ [UI-DELETE] User clicked delete for ID: ${id}`);
    
    try {
      // Mark as deleting for visual feedback
      setDeletingIds(prev => new Set(prev).add(id));
      console.log(`🗑️ [UI-DELETE] Marked ID: ${id} as deleting`);

      // Call the delete API
      const success = await deletePerformanceData(id);

      if (success) {
        console.log(`✅ [UI-DELETE] Delete API successful for ID: ${id}`);

        // Show success toast immediately
        toast({
          title: "✅ Deleted Successfully",
          description: "Changes synced in real-time across all tabs and users.",
          variant: "default"
        });

      } else {
        throw new Error('Delete operation returned false');
      }
    } catch (error) {
      console.error(`❌ [UI-DELETE] Error deleting entry with ID: ${id}`, error);
      toast({
        title: "❌ Delete Failed",
        description: `Failed to delete entry: ${error.message || 'Unknown error'}. Please try again.`,
        variant: "destructive",
      });
    } finally {
      // Clean up the deleting state
      setDeletingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        console.log(`🗑️ [UI-DELETE] Unmarked ID: ${id} as deleting`);
        return newSet;
      });
    }
  };

  const formatTalkTimeForEdit = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Performance History
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                View and edit past performance entries ({filteredData.length} entries)
                {timeFrame === "Daily" && selectedDate && (
                  <span className="ml-2 text-blue-600">
                    • Filtered by: {format(selectedDate, "MMM dd, yyyy")}
                  </span>
                )}
                {timeFrame === "Weekly" && selectedWeek && (
                  <span className="ml-2 text-blue-600">
                    • Filtered by: {selectedWeek}
                  </span>
                )}
                {timeFrame === "Monthly" && selectedMonth && selectedMonth !== "All Time" && (
                  <span className="ml-2 text-blue-600">
                    • Filtered by: {selectedMonth}
                  </span>
                )}
              </p>
            </div>
            <Button
              onClick={handleRefresh}
              disabled={isRefreshing}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh Data'}
            </Button>
          </div>
        </div>

        {/* Data Fetch Error Alert */}
        {dataFetchError && (
          <div className="px-6 py-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {dataFetchError}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Main Table Container */}
        <div className="relative">
          <div className="overflow-x-auto overflow-y-visible">
            <Table className="min-w-[1200px]">
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[120px]">Date</TableHead>
                <TableHead className="min-w-[100px]">Week</TableHead>
                <TableHead className="min-w-[120px]">Agent</TableHead>
                <TableHead className="min-w-[120px]">Project</TableHead>
                <TableHead className="min-w-[80px]">Dials</TableHead>
                <TableHead className="min-w-[90px]">Connected</TableHead>
                <TableHead className="min-w-[100px]">Talk Time</TableHead>
                <TableHead className="min-w-[90px]">Scheduled</TableHead>
                <TableHead className="min-w-[90px]">Successful</TableHead>
                <TableHead className="min-w-[110px]">FOP Scheduled</TableHead>
                <TableHead className="min-w-[110px]">FOP Successful</TableHead>
                <TableHead className="min-w-[120px]">FOP Projects</TableHead>
                <TableHead className="min-w-[120px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.map((item) => {
                const isDeleting = deletingIds.has(item.id);
                
                return (
                  <TableRow 
                    key={item.id} 
                    className={cn(
                      "hover:bg-gray-50 transition-colors duration-200",
                      isDeleting && "opacity-50 pointer-events-none bg-red-50"
                    )}
                  >
                    {/* Date Field - Editable */}
                    <TableCell>
                      {editingId === item.id ? (
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !editData.date && "text-muted-foreground"
                              )}
                            >
                              <Calendar className="mr-2 h-4 w-4" />
                              {editData.date ? format(new Date(editData.date), "MMM dd, yyyy") : "Pick a date"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent
                            className="w-auto p-0 bg-white border shadow-lg z-[9999] animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2"
                            side="bottom"
                            align="start"
                            sideOffset={5}
                          >
                            <CalendarComponent
                              mode="single"
                              selected={editData.date ? new Date(editData.date) : undefined}
                              onSelect={(date) => setEditData({...editData, date: date?.toISOString().split('T')[0] || ''})}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      ) : (
                        format(new Date(item.date), "MMM dd, yyyy")
                      )}
                    </TableCell>

                    {/* Week Field - Editable */}
                    <TableCell>
                      {editingId === item.id ? (
                        <UniversalDropdown
                          options={weekDropdownOptions}
                          placeholder="Select week"
                          value={weekDropdownOptions.find(option => option.value === editData.week) || null}
                          onChange={(option) => setEditData({...editData, week: option.value})}
                          className="w-full"
                          minWidth="150px"
                        />
                      ) : (
                        item.week || "-"
                      )}
                    </TableCell>

                    {/* Agent Field - Editable */}
                    <TableCell>
                      {editingId === item.id ? (
                        <UniversalDropdown
                          options={agentDropdownOptions}
                          placeholder="Select agent"
                          value={agentDropdownOptions.find(option => option.value === editData.agent_id) || null}
                          onChange={(option) => setEditData({...editData, agent_id: option.value})}
                          className="w-full"
                          minWidth="180px"
                        />
                      ) : (
                        getAgentById(item.agent_id)?.name || "Unknown"
                      )}
                    </TableCell>

                    {/* Project Field - Editable */}
                    <TableCell>
                      {editingId === item.id ? (
                        <UniversalDropdown
                          options={projectDropdownOptions}
                          placeholder="Select project"
                          value={projectDropdownOptions.find(option => option.value === editData.project_id) || null}
                          onChange={(option) => setEditData({...editData, project_id: option.value})}
                          className="w-full"
                          minWidth="180px"
                        />
                      ) : (
                        getProjectById(item.project_id)?.name || "Unknown"
                      )}
                    </TableCell>
                    
                    {/* Editable fields */}
                    <TableCell>
                      {editingId === item.id ? (
                        <Input
                          type="number"
                          min="0"
                          value={editData.dials || 0}
                          onChange={(e) => setEditData({...editData, dials: parseInt(e.target.value) || 0})}
                          className="w-20"
                        />
                      ) : (
                        item.dials
                      )}
                    </TableCell>
                    
                    <TableCell>
                      {editingId === item.id ? (
                        <Input
                          type="number"
                          min="0"
                          value={editData.connected || 0}
                          onChange={(e) => setEditData({...editData, connected: parseInt(e.target.value) || 0})}
                          className="w-20"
                        />
                      ) : (
                        item.connected
                      )}
                    </TableCell>
                    
                    <TableCell>
                      {editingId === item.id ? (
                        <TimeInput
                          value={formatTalkTimeForEdit(editData.talk_time || 0)}
                          onChange={(value) => setEditData({...editData, talk_time: parseTalkTime(value)})}
                          className="w-24"
                        />
                      ) : (
                        formatTalkTime(item.talk_time)
                      )}
                    </TableCell>
                    
                    <TableCell>
                      {editingId === item.id ? (
                        <Input
                          type="number"
                          min="0"
                          value={editData.scheduled_meetings || 0}
                          onChange={(e) => setEditData({...editData, scheduled_meetings: parseInt(e.target.value) || 0})}
                          className="w-20"
                        />
                      ) : (
                        item.scheduled_meetings
                      )}
                    </TableCell>
                    
                    <TableCell>
                      {editingId === item.id ? (
                        <Input
                          type="number"
                          min="0"
                          value={editData.successful_meetings || 0}
                          onChange={(e) => setEditData({...editData, successful_meetings: parseInt(e.target.value) || 0})}
                          className="w-20"
                        />
                      ) : (
                        item.successful_meetings
                      )}
                    </TableCell>
                    
                    <TableCell>
                      {editingId === item.id ? (
                        <Input
                          type="number"
                          min="0"
                          value={editData.fop_scheduled || 0}
                          onChange={(e) => setEditData({...editData, fop_scheduled: parseInt(e.target.value) || 0})}
                          className="w-20"
                        />
                      ) : (
                        item.fop_scheduled
                      )}
                    </TableCell>
                    
                    <TableCell>
                      {editingId === item.id ? (
                        <Input
                          type="number"
                          min="0"
                          value={editData.fop_successful || 0}
                          onChange={(e) => setEditData({...editData, fop_successful: parseInt(e.target.value) || 0})}
                          className="w-20"
                        />
                      ) : (
                        item.fop_successful
                      )}
                    </TableCell>

                    {/* FOP Projects Field */}
                    <TableCell>
                      {editingId === item.id ? (
                        <div className="space-y-2">
                          <UniversalDropdown
                            options={fopProjectDropdownOptions}
                            placeholder="Add FOP project"
                            value={null}
                            onChange={(option) => {
                              if (!selectedFopProjects.includes(option.value)) {
                                const newProjects = [...selectedFopProjects, option.value];
                                setSelectedFopProjects(newProjects);
                                setEditData({...editData, fop_projects: newProjects});
                              }
                            }}
                            className="w-full"
                            minWidth="200px"
                          />
                          <div className="flex flex-wrap gap-1">
                            {selectedFopProjects.map((projectId) => {
                              const project = getProjectById(projectId);
                              return (
                                <div
                                  key={projectId}
                                  className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 rounded-md text-xs"
                                >
                                  {project?.name || 'Unknown'}
                                  <button
                                    onClick={() => {
                                      const newProjects = selectedFopProjects.filter(id => id !== projectId);
                                      setSelectedFopProjects(newProjects);
                                      setEditData({...editData, fop_projects: newProjects});
                                    }}
                                    className="ml-1 text-green-600 hover:text-green-800"
                                  >
                                    <X className="h-3 w-3" />
                                  </button>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-wrap gap-1">
                          {(item.fop_projects || []).map((projectId) => {
                            const project = getProjectById(projectId);
                            return (
                              <div
                                key={projectId}
                                className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 rounded-md text-xs"
                              >
                                {project?.name || 'Unknown'}
                              </div>
                            );
                          })}
                          {(!item.fop_projects || item.fop_projects.length === 0) && (
                            <span className="text-gray-400 text-xs">No projects</span>
                          )}
                        </div>
                      )}
                    </TableCell>

                    <TableCell>
                      <div className="flex gap-2">
                        {editingId === item.id ? (
                          <>
                            <Button
                              size="sm"
                              onClick={() => saveEdit(item.id)}
                              className="h-8 w-8 p-0"
                              disabled={isUpdating}
                            >
                              {isUpdating ? (
                                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                              ) : (
                                <Save className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={cancelEdit}
                              className="h-8 w-8 p-0"
                              disabled={isUpdating}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => startEdit(item)}
                              className="h-8 w-8 p-0"
                              disabled={isDeleting}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                  disabled={isDeleting}
                                >
                                  {isDeleting ? (
                                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600"></div>
                                  ) : (
                                    <Trash2 className="h-4 w-4" />
                                  )}
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent className="bg-white">
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Performance Entry</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete this performance entry? This action cannot be undone and will be synced across all tabs and users in real-time.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => deleteEntry(item.id)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
            </Table>
          </div>
        </div>

        {filteredData.length === 0 && !isRefreshing && (
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="h-12 w-12 mx-auto text-gray-400 mb-3" />
            <p className="text-gray-500 font-medium">No performance data found</p>
            <p className="text-sm text-gray-400 mt-1">
              Try adjusting your date filters or check if data exists for the selected period.
            </p>
            <Button
              onClick={handleRefresh}
              variant="outline"
              size="sm"
              className="mt-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Data
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceHistoryView;
